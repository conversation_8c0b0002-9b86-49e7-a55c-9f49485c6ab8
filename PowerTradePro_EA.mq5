//+------------------------------------------------------------------+
//|                                            PowerTradePro_EA.mq5 |
//|                                  Copyright 2024, PowerTrade Pro |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, PowerTrade Pro"
#property link      "https://www.mql5.com"
#property version   "1.00"
#property description "PowerTrade Pro: 10-Indicator Confluence Trading Strategy"
#property description "Requires minimum 7/10 indicators alignment for trade entry"

#include <Trade\Trade.mqh>
#include <Trade\PositionInfo.mqh>
#include <Trade\AccountInfo.mqh>

//--- Input Parameters
input group "=== STRATEGY SETTINGS ==="
input int      MinConfluence = 7;           // Minimum indicators required (7-10)
input bool     EnableBuySignals = true;     // Enable BUY signals
input bool     EnableSellSignals = true;    // Enable SELL signals

input group "=== RISK MANAGEMENT ==="
input double   RiskPercent_7_Indicators = 1.0;   // Risk % for 7 indicators
input double   RiskPercent_8_Indicators = 1.5;   // Risk % for 8 indicators  
input double   RiskPercent_9_Indicators = 2.0;   // Risk % for 9 indicators
input double   RiskPercent_10_Indicators = 2.5;  // Risk % for 10 indicators
input double   MaxDailyLoss = 6.0;         // Maximum daily loss %
input double   MaxWeeklyLoss = 12.0;       // Maximum weekly loss %
input double   MaxMonthlyLoss = 20.0;      // Maximum monthly loss %
input int      MaxConcurrentTrades = 3;    // Maximum concurrent trades

input group "=== STOP LOSS & TAKE PROFIT ==="
input double   ATR_Multiplier = 2.0;       // ATR multiplier for stop loss
input int      MinStopLoss = 20;           // Minimum stop loss in pips
input int      MaxStopLoss = 100;          // Maximum stop loss in pips
input double   TP1_Multiplier = 1.5;       // TP1 = SL distance × this
input double   TP2_Multiplier = 2.5;       // TP2 = SL distance × this
input double   TP1_ClosePercent = 50;      // % to close at TP1
input double   TP2_ClosePercent = 30;      // % to close at TP2

input group "=== EMA RIBBON SETTINGS ==="
input int      EMA_Fast = 20;              // Fast EMA period
input int      EMA_Medium = 50;            // Medium EMA period
input int      EMA_Slow = 100;             // Slow EMA period

input group "=== ICHIMOKU SETTINGS ==="
input int      Ichimoku_Tenkan = 9;        // Tenkan-sen period
input int      Ichimoku_Kijun = 26;        // Kijun-sen period
input int      Ichimoku_Senkou = 52;       // Senkou Span B period

input group "=== PARABOLIC SAR SETTINGS ==="
input double   SAR_Step = 0.02;            // SAR step
input double   SAR_Maximum = 0.2;          // SAR maximum

input group "=== MOMENTUM INDICATORS ==="
input int      RSI_Period = 14;            // RSI period
input int      RSI_Overbought = 70;        // RSI overbought level
input int      RSI_Oversold = 30;          // RSI oversold level
input int      MACD_Fast = 12;             // MACD fast EMA
input int      MACD_Slow = 26;             // MACD slow EMA
input int      MACD_Signal = 9;            // MACD signal line
input int      Stoch_K = 14;               // Stochastic %K period
input int      Stoch_D = 3;                // Stochastic %D period
input int      Stoch_Slowing = 3;          // Stochastic slowing

input group "=== VOLATILITY INDICATORS ==="
input int      BB_Period = 20;             // Bollinger Bands period
input double   BB_Deviation = 2.0;         // Bollinger Bands deviation
input int      ATR_Period = 14;            // ATR period
input int      Volume_Period = 20;         // Volume average period

input group "=== SESSION FILTERS ==="
input bool     EnableSessionFilter = true; // Enable session filtering
input int      SessionStartHour = 12;      // Session start hour (GMT)
input int      SessionEndHour = 16;        // Session end hour (GMT)
input bool     AvoidNews = true;           // Avoid trading during news
input int      NewsAvoidMinutes = 30;      // Minutes to avoid before/after news

input group "=== GENERAL SETTINGS ==="
input ulong    MagicNumber = 123456;       // Magic number
input string   TradeComment = "PowerTradePro"; // Trade comment
input bool     EnableAlerts = true;        // Enable alerts
input bool     EnableLogging = true;       // Enable detailed logging

//--- Global Variables
CTrade         trade;
CPositionInfo  position;
CAccountInfo   account;

// Indicator handles
int handle_EMA_Fast, handle_EMA_Medium, handle_EMA_Slow;
int handle_Ichimoku;
int handle_SAR;
int handle_RSI;
int handle_MACD;
int handle_Stochastic;
int handle_BB;
int handle_ATR;

// Indicator buffers
double EMA_Fast[], EMA_Medium[], EMA_Slow[];
double Ichimoku_Tenkan[], Ichimoku_Kijun[], Ichimoku_SpanA[], Ichimoku_SpanB[];
double SAR[];
double RSI[];
double MACD_Main[], MACD_Signal[], MACD_Histogram[];
double Stoch_Main[], Stoch_Signal[];
double BB_Upper[], BB_Middle[], BB_Lower[];
double ATR[];

// VWAP and OBV calculation variables
double VWAP_Sum = 0, Volume_Sum = 0;
double OBV_Current = 0, OBV_Previous = 0;

// Risk management variables
double DailyStartBalance = 0;
double WeeklyStartBalance = 0;
double MonthlyStartBalance = 0;
datetime LastDayCheck = 0;
datetime LastWeekCheck = 0;
datetime LastMonthCheck = 0;

// Trade management
struct TradeInfo {
    ulong ticket;
    double entry_price;
    double stop_loss;
    double take_profit_1;
    double take_profit_2;
    bool tp1_hit;
    bool tp2_hit;
    bool breakeven_moved;
    double original_volume;
};

TradeInfo ActiveTrades[];

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    // Set magic number for trade operations
    trade.SetExpertMagicNumber(MagicNumber);
    
    // Initialize indicator handles
    if(!InitializeIndicators()) {
        Print("Failed to initialize indicators");
        return INIT_FAILED;
    }
    
    // Initialize risk management
    InitializeRiskManagement();
    
    // Set array properties
    SetArrayProperties();
    
    Print("PowerTrade Pro EA initialized successfully");
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    // Release indicator handles
    ReleaseIndicators();
    
    Print("PowerTrade Pro EA deinitialized. Reason: ", reason);
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // Check risk management limits
    if(!CheckRiskLimits()) {
        if(EnableLogging) Print("Risk limits exceeded - no new trades");
        return;
    }

    // Check session filter
    if(EnableSessionFilter && !IsWithinTradingSession()) {
        return;
    }

    // Check news filter
    if(AvoidNews && IsNewsTime()) {
        if(EnableLogging) Print("News time - avoiding new trades");
        return;
    }

    // Update indicator values
    if(!UpdateIndicatorValues()) {
        return;
    }

    // Manage existing trades
    ManageExistingTrades();

    // Check for new signals if we have room for more trades
    if(GetActiveTradesCount() < MaxConcurrentTrades) {
        CheckForNewSignals();
    }

    // Log statistics periodically (every hour)
    static datetime last_stats_time = 0;
    if(TimeCurrent() - last_stats_time >= 3600) {
        LogTradeStatistics();
        last_stats_time = TimeCurrent();
    }
}

//+------------------------------------------------------------------+
//| Initialize all indicators                                        |
//+------------------------------------------------------------------+
bool InitializeIndicators()
{
    // EMA indicators
    handle_EMA_Fast = iMA(_Symbol, PERIOD_CURRENT, EMA_Fast, 0, MODE_EMA, PRICE_CLOSE);
    handle_EMA_Medium = iMA(_Symbol, PERIOD_CURRENT, EMA_Medium, 0, MODE_EMA, PRICE_CLOSE);
    handle_EMA_Slow = iMA(_Symbol, PERIOD_CURRENT, EMA_Slow, 0, MODE_EMA, PRICE_CLOSE);
    
    // Ichimoku
    handle_Ichimoku = iIchimoku(_Symbol, PERIOD_CURRENT, Ichimoku_Tenkan, Ichimoku_Kijun, Ichimoku_Senkou);
    
    // Parabolic SAR
    handle_SAR = iSAR(_Symbol, PERIOD_CURRENT, SAR_Step, SAR_Maximum);
    
    // RSI
    handle_RSI = iRSI(_Symbol, PERIOD_CURRENT, RSI_Period, PRICE_CLOSE);
    
    // MACD
    handle_MACD = iMACD(_Symbol, PERIOD_CURRENT, MACD_Fast, MACD_Slow, MACD_Signal, PRICE_CLOSE);
    
    // Stochastic
    handle_Stochastic = iStochastic(_Symbol, PERIOD_CURRENT, Stoch_K, Stoch_D, Stoch_Slowing, MODE_SMA, STO_LOWHIGH);
    
    // Bollinger Bands
    handle_BB = iBands(_Symbol, PERIOD_CURRENT, BB_Period, 0, BB_Deviation, PRICE_CLOSE);
    
    // ATR
    handle_ATR = iATR(_Symbol, PERIOD_CURRENT, ATR_Period);
    
    // Check if all handles are valid
    if(handle_EMA_Fast == INVALID_HANDLE || handle_EMA_Medium == INVALID_HANDLE || 
       handle_EMA_Slow == INVALID_HANDLE || handle_Ichimoku == INVALID_HANDLE ||
       handle_SAR == INVALID_HANDLE || handle_RSI == INVALID_HANDLE ||
       handle_MACD == INVALID_HANDLE || handle_Stochastic == INVALID_HANDLE ||
       handle_BB == INVALID_HANDLE || handle_ATR == INVALID_HANDLE) {
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Release indicator handles                                        |
//+------------------------------------------------------------------+
void ReleaseIndicators()
{
    IndicatorRelease(handle_EMA_Fast);
    IndicatorRelease(handle_EMA_Medium);
    IndicatorRelease(handle_EMA_Slow);
    IndicatorRelease(handle_Ichimoku);
    IndicatorRelease(handle_SAR);
    IndicatorRelease(handle_RSI);
    IndicatorRelease(handle_MACD);
    IndicatorRelease(handle_Stochastic);
    IndicatorRelease(handle_BB);
    IndicatorRelease(handle_ATR);
}

//+------------------------------------------------------------------+
//| Initialize risk management                                       |
//+------------------------------------------------------------------+
void InitializeRiskManagement()
{
    DailyStartBalance = account.Balance();
    WeeklyStartBalance = account.Balance();
    MonthlyStartBalance = account.Balance();
    LastDayCheck = TimeCurrent();
    LastWeekCheck = TimeCurrent();
    LastMonthCheck = TimeCurrent();
}

//+------------------------------------------------------------------+
//| Set array properties                                             |
//+------------------------------------------------------------------+
void SetArrayProperties()
{
    ArraySetAsSeries(EMA_Fast, true);
    ArraySetAsSeries(EMA_Medium, true);
    ArraySetAsSeries(EMA_Slow, true);
    ArraySetAsSeries(Ichimoku_Tenkan, true);
    ArraySetAsSeries(Ichimoku_Kijun, true);
    ArraySetAsSeries(Ichimoku_SpanA, true);
    ArraySetAsSeries(Ichimoku_SpanB, true);
    ArraySetAsSeries(SAR, true);
    ArraySetAsSeries(RSI, true);
    ArraySetAsSeries(MACD_Main, true);
    ArraySetAsSeries(MACD_Signal, true);
    ArraySetAsSeries(MACD_Histogram, true);
    ArraySetAsSeries(Stoch_Main, true);
    ArraySetAsSeries(Stoch_Signal, true);
    ArraySetAsSeries(BB_Upper, true);
    ArraySetAsSeries(BB_Middle, true);
    ArraySetAsSeries(BB_Lower, true);
    ArraySetAsSeries(ATR, true);
}

//+------------------------------------------------------------------+
//| Update all indicator values                                      |
//+------------------------------------------------------------------+
bool UpdateIndicatorValues()
{
    // Copy indicator values
    if(CopyBuffer(handle_EMA_Fast, 0, 0, 3, EMA_Fast) <= 0) return false;
    if(CopyBuffer(handle_EMA_Medium, 0, 0, 3, EMA_Medium) <= 0) return false;
    if(CopyBuffer(handle_EMA_Slow, 0, 0, 3, EMA_Slow) <= 0) return false;

    if(CopyBuffer(handle_Ichimoku, 0, 0, 3, Ichimoku_Tenkan) <= 0) return false;
    if(CopyBuffer(handle_Ichimoku, 1, 0, 3, Ichimoku_Kijun) <= 0) return false;
    if(CopyBuffer(handle_Ichimoku, 2, 0, 3, Ichimoku_SpanA) <= 0) return false;
    if(CopyBuffer(handle_Ichimoku, 3, 0, 3, Ichimoku_SpanB) <= 0) return false;

    if(CopyBuffer(handle_SAR, 0, 0, 3, SAR) <= 0) return false;
    if(CopyBuffer(handle_RSI, 0, 0, 3, RSI) <= 0) return false;

    if(CopyBuffer(handle_MACD, 0, 0, 3, MACD_Main) <= 0) return false;
    if(CopyBuffer(handle_MACD, 1, 0, 3, MACD_Signal) <= 0) return false;

    if(CopyBuffer(handle_Stochastic, 0, 0, 3, Stoch_Main) <= 0) return false;
    if(CopyBuffer(handle_Stochastic, 1, 0, 3, Stoch_Signal) <= 0) return false;

    if(CopyBuffer(handle_BB, 0, 0, 3, BB_Upper) <= 0) return false;
    if(CopyBuffer(handle_BB, 1, 0, 3, BB_Middle) <= 0) return false;
    if(CopyBuffer(handle_BB, 2, 0, 3, BB_Lower) <= 0) return false;

    if(CopyBuffer(handle_ATR, 0, 0, 3, ATR) <= 0) return false;

    // Update VWAP and OBV
    UpdateVWAP();
    UpdateOBV();

    return true;
}

//+------------------------------------------------------------------+
//| Update VWAP calculation                                          |
//+------------------------------------------------------------------+
void UpdateVWAP()
{
    MqlRates rates[];
    if(CopyRates(_Symbol, PERIOD_CURRENT, 0, 1, rates) <= 0) return;

    double typical_price = (rates[0].high + rates[0].low + rates[0].close) / 3.0;
    double volume = (double)rates[0].tick_volume;

    VWAP_Sum += typical_price * volume;
    Volume_Sum += volume;

    // Reset VWAP at start of new day
    static datetime last_day = 0;
    datetime current_day = iTime(_Symbol, PERIOD_D1, 0);
    if(current_day != last_day) {
        VWAP_Sum = typical_price * volume;
        Volume_Sum = volume;
        last_day = current_day;
    }
}

//+------------------------------------------------------------------+
//| Update OBV calculation                                           |
//+------------------------------------------------------------------+
void UpdateOBV()
{
    MqlRates rates[];
    if(CopyRates(_Symbol, PERIOD_CURRENT, 0, 2, rates) <= 0) return;

    OBV_Previous = OBV_Current;

    if(rates[0].close > rates[1].close) {
        OBV_Current += rates[0].tick_volume;
    } else if(rates[0].close < rates[1].close) {
        OBV_Current -= rates[0].tick_volume;
    }
    // If close == previous close, OBV remains unchanged
}

//+------------------------------------------------------------------+
//| Check risk management limits                                     |
//+------------------------------------------------------------------+
bool CheckRiskLimits()
{
    double current_balance = account.Balance();
    datetime current_time = TimeCurrent();

    // Check daily loss limit
    MqlDateTime dt;
    TimeToStruct(current_time, dt);
    datetime day_start = StructToTime(dt) - (dt.hour * 3600 + dt.min * 60 + dt.sec);

    if(day_start != LastDayCheck) {
        DailyStartBalance = current_balance;
        LastDayCheck = day_start;
    }

    double daily_loss_percent = (DailyStartBalance - current_balance) / DailyStartBalance * 100;
    if(daily_loss_percent >= MaxDailyLoss) {
        if(EnableLogging) Print("Daily loss limit exceeded: ", daily_loss_percent, "%");
        return false;
    }

    // Check weekly loss limit
    int day_of_week = dt.day_of_week;
    datetime week_start = day_start - (day_of_week * 86400);

    if(week_start != LastWeekCheck) {
        WeeklyStartBalance = current_balance;
        LastWeekCheck = week_start;
    }

    double weekly_loss_percent = (WeeklyStartBalance - current_balance) / WeeklyStartBalance * 100;
    if(weekly_loss_percent >= MaxWeeklyLoss) {
        if(EnableLogging) Print("Weekly loss limit exceeded: ", weekly_loss_percent, "%");
        return false;
    }

    // Check monthly loss limit
    datetime month_start = StructToTime(dt) - ((dt.day - 1) * 86400 + dt.hour * 3600 + dt.min * 60 + dt.sec);

    if(month_start != LastMonthCheck) {
        MonthlyStartBalance = current_balance;
        LastMonthCheck = month_start;
    }

    double monthly_loss_percent = (MonthlyStartBalance - current_balance) / MonthlyStartBalance * 100;
    if(monthly_loss_percent >= MaxMonthlyLoss) {
        if(EnableLogging) Print("Monthly loss limit exceeded: ", monthly_loss_percent, "%");
        return false;
    }

    return true;
}

//+------------------------------------------------------------------+
//| Check if within trading session                                  |
//+------------------------------------------------------------------+
bool IsWithinTradingSession()
{
    MqlDateTime dt;
    TimeToStruct(TimeCurrent(), dt);

    int current_hour = dt.hour;

    if(SessionStartHour <= SessionEndHour) {
        return (current_hour >= SessionStartHour && current_hour < SessionEndHour);
    } else {
        // Handle overnight sessions
        return (current_hour >= SessionStartHour || current_hour < SessionEndHour);
    }
}

//+------------------------------------------------------------------+
//| Get active trades count                                          |
//+------------------------------------------------------------------+
int GetActiveTradesCount()
{
    int count = 0;
    for(int i = PositionsTotal() - 1; i >= 0; i--) {
        if(position.SelectByIndex(i)) {
            if(position.Symbol() == _Symbol && position.Magic() == MagicNumber) {
                count++;
            }
        }
    }
    return count;
}

//+------------------------------------------------------------------+
//| Check for new trading signals                                    |
//+------------------------------------------------------------------+
void CheckForNewSignals()
{
    int confluence_score = 0;
    bool primary_conditions_met = false;
    int secondary_conditions_met = 0;

    // Check BUY signals
    if(EnableBuySignals) {
        confluence_score = CalculateBuyConfluence(primary_conditions_met, secondary_conditions_met);

        if(confluence_score >= MinConfluence && primary_conditions_met && secondary_conditions_met >= 3) {
            if(ValidateSignal(confluence_score, true)) {
                ExecuteBuyOrder(confluence_score);
            }
        }
    }

    // Check SELL signals
    if(EnableSellSignals) {
        confluence_score = CalculateSellConfluence(primary_conditions_met, secondary_conditions_met);

        if(confluence_score >= MinConfluence && primary_conditions_met && secondary_conditions_met >= 3) {
            if(ValidateSignal(confluence_score, false)) {
                ExecuteSellOrder(confluence_score);
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Calculate BUY confluence score                                   |
//+------------------------------------------------------------------+
int CalculateBuyConfluence(bool &primary_met, int &secondary_met)
{
    int score = 0;
    primary_met = true;
    secondary_met = 0;

    double current_price = SymbolInfoDouble(_Symbol, SYMBOL_BID);

    // PRIMARY CONDITIONS (Must have all 4)

    // 1. EMA Ribbon: EMA20 > EMA50 > EMA100
    if(EMA_Fast[0] > EMA_Medium[0] && EMA_Medium[0] > EMA_Slow[0]) {
        score++;
    } else {
        primary_met = false;
    }

    // 2. Ichimoku: Price above cloud AND Tenkan > Kijun
    double cloud_top = MathMax(Ichimoku_SpanA[0], Ichimoku_SpanB[0]);
    if(current_price > cloud_top && Ichimoku_Tenkan[0] > Ichimoku_Kijun[0]) {
        score++;
    } else {
        primary_met = false;
    }

    // 3. VWAP: Price trading above VWAP
    double current_vwap = (Volume_Sum > 0) ? VWAP_Sum / Volume_Sum : current_price;
    if(current_price > current_vwap) {
        score++;
    } else {
        primary_met = false;
    }

    // 4. Parabolic SAR: Dots below price
    if(SAR[0] < current_price) {
        score++;
    } else {
        primary_met = false;
    }

    // SECONDARY CONDITIONS (Need 3 of 6)

    // 5. RSI: > 50 and rising (not overbought >70)
    if(RSI[0] > 50 && RSI[0] < RSI_Overbought && RSI[0] > RSI[1]) {
        score++;
        secondary_met++;
    }

    // 6. MACD: Line above signal AND above zero line
    if(MACD_Main[0] > MACD_Signal[0] && MACD_Main[0] > 0) {
        score++;
        secondary_met++;
    }

    // 7. Stochastic: %K crosses above %D or both below 50
    if((Stoch_Main[0] > Stoch_Signal[0] && Stoch_Main[1] <= Stoch_Signal[1]) ||
       (Stoch_Main[0] < 50 && Stoch_Signal[0] < 50)) {
        score++;
        secondary_met++;
    }

    // 8. Bollinger Bands: Price in upper half OR breakout above upper band
    double bb_middle = BB_Middle[0];
    if(current_price > bb_middle || current_price > BB_Upper[0]) {
        score++;
        secondary_met++;
    }

    // 9. Volume: Above 20-period average (simplified check)
    MqlRates rates[];
    if(CopyRates(_Symbol, PERIOD_CURRENT, 0, Volume_Period + 1, rates) > 0) {
        double avg_volume = 0;
        for(int i = 1; i <= Volume_Period; i++) {
            avg_volume += rates[i].tick_volume;
        }
        avg_volume /= Volume_Period;

        if(rates[0].tick_volume > avg_volume) {
            score++;
            secondary_met++;
        }
    }

    // 10. OBV: Rising or making higher highs
    if(OBV_Current > OBV_Previous) {
        score++;
        secondary_met++;
    }

    return score;
}

//+------------------------------------------------------------------+
//| Calculate SELL confluence score                                  |
//+------------------------------------------------------------------+
int CalculateSellConfluence(bool &primary_met, int &secondary_met)
{
    int score = 0;
    primary_met = true;
    secondary_met = 0;

    double current_price = SymbolInfoDouble(_Symbol, SYMBOL_ASK);

    // PRIMARY CONDITIONS (Must have all 4)

    // 1. EMA Ribbon: EMA20 < EMA50 < EMA100
    if(EMA_Fast[0] < EMA_Medium[0] && EMA_Medium[0] < EMA_Slow[0]) {
        score++;
    } else {
        primary_met = false;
    }

    // 2. Ichimoku: Price below cloud AND Tenkan < Kijun
    double cloud_bottom = MathMin(Ichimoku_SpanA[0], Ichimoku_SpanB[0]);
    if(current_price < cloud_bottom && Ichimoku_Tenkan[0] < Ichimoku_Kijun[0]) {
        score++;
    } else {
        primary_met = false;
    }

    // 3. VWAP: Price trading below VWAP
    double current_vwap = (Volume_Sum > 0) ? VWAP_Sum / Volume_Sum : current_price;
    if(current_price < current_vwap) {
        score++;
    } else {
        primary_met = false;
    }

    // 4. Parabolic SAR: Dots above price
    if(SAR[0] > current_price) {
        score++;
    } else {
        primary_met = false;
    }

    // SECONDARY CONDITIONS (Need 3 of 6)

    // 5. RSI: < 50 and falling (not oversold <30)
    if(RSI[0] < 50 && RSI[0] > RSI_Oversold && RSI[0] < RSI[1]) {
        score++;
        secondary_met++;
    }

    // 6. MACD: Line below signal AND below zero line
    if(MACD_Main[0] < MACD_Signal[0] && MACD_Main[0] < 0) {
        score++;
        secondary_met++;
    }

    // 7. Stochastic: %K crosses below %D or both above 50
    if((Stoch_Main[0] < Stoch_Signal[0] && Stoch_Main[1] >= Stoch_Signal[1]) ||
       (Stoch_Main[0] > 50 && Stoch_Signal[0] > 50)) {
        score++;
        secondary_met++;
    }

    // 8. Bollinger Bands: Price in lower half OR breakout below lower band
    double bb_middle = BB_Middle[0];
    if(current_price < bb_middle || current_price < BB_Lower[0]) {
        score++;
        secondary_met++;
    }

    // 9. Volume: Above 20-period average
    MqlRates rates[];
    if(CopyRates(_Symbol, PERIOD_CURRENT, 0, Volume_Period + 1, rates) > 0) {
        double avg_volume = 0;
        for(int i = 1; i <= Volume_Period; i++) {
            avg_volume += rates[i].tick_volume;
        }
        avg_volume /= Volume_Period;

        if(rates[0].tick_volume > avg_volume) {
            score++;
            secondary_met++;
        }
    }

    // 10. OBV: Falling or making lower lows
    if(OBV_Current < OBV_Previous) {
        score++;
        secondary_met++;
    }

    return score;
}

//+------------------------------------------------------------------+
//| Execute BUY order                                                |
//+------------------------------------------------------------------+
void ExecuteBuyOrder(int confluence_score)
{
    double entry_price = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    double atr_value = ATR[0];
    double point = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
    int digits = (int)SymbolInfoInteger(_Symbol, SYMBOL_DIGITS);

    // Calculate stop loss
    double stop_distance = atr_value * ATR_Multiplier;
    double min_stop_distance = MinStopLoss * point * 10; // Convert pips to price
    double max_stop_distance = MaxStopLoss * point * 10;

    stop_distance = MathMax(stop_distance, min_stop_distance);
    stop_distance = MathMin(stop_distance, max_stop_distance);

    double stop_loss = entry_price - stop_distance;

    // Calculate take profit levels
    double tp1_distance = stop_distance * TP1_Multiplier;
    double tp2_distance = stop_distance * TP2_Multiplier;
    double take_profit_1 = entry_price + tp1_distance;
    double take_profit_2 = entry_price + tp2_distance;

    // Calculate position size based on confluence score
    double risk_percent = GetRiskPercent(confluence_score);
    double risk_amount = account.Balance() * risk_percent / 100.0;
    double lot_size = risk_amount / (stop_distance / point);

    // Normalize lot size
    double min_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
    double max_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
    double lot_step = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);

    lot_size = MathMax(lot_size, min_lot);
    lot_size = MathMin(lot_size, max_lot);
    lot_size = NormalizeDouble(lot_size / lot_step, 0) * lot_step;

    // Execute the trade
    if(trade.Buy(lot_size, _Symbol, entry_price, stop_loss, 0, TradeComment)) {
        ulong ticket = trade.ResultOrder();

        // Store trade information for management
        TradeInfo new_trade;
        new_trade.ticket = ticket;
        new_trade.entry_price = entry_price;
        new_trade.stop_loss = stop_loss;
        new_trade.take_profit_1 = take_profit_1;
        new_trade.take_profit_2 = take_profit_2;
        new_trade.tp1_hit = false;
        new_trade.tp2_hit = false;
        new_trade.breakeven_moved = false;
        new_trade.original_volume = lot_size;

        AddTradeToArray(new_trade);

        if(EnableLogging) {
            Print("BUY order executed - Ticket: ", ticket,
                  " Confluence: ", confluence_score, "/10",
                  " Lot size: ", lot_size,
                  " Entry: ", entry_price,
                  " SL: ", stop_loss,
                  " TP1: ", take_profit_1,
                  " TP2: ", take_profit_2);
        }

        if(EnableAlerts) {
            Alert("PowerTrade Pro: BUY signal executed with ", confluence_score, "/10 confluence");
        }
    } else {
        if(EnableLogging) Print("Failed to execute BUY order. Error: ", GetLastError());
    }
}

//+------------------------------------------------------------------+
//| Execute SELL order                                               |
//+------------------------------------------------------------------+
void ExecuteSellOrder(int confluence_score)
{
    double entry_price = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    double atr_value = ATR[0];
    double point = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
    int digits = (int)SymbolInfoInteger(_Symbol, SYMBOL_DIGITS);

    // Calculate stop loss
    double stop_distance = atr_value * ATR_Multiplier;
    double min_stop_distance = MinStopLoss * point * 10;
    double max_stop_distance = MaxStopLoss * point * 10;

    stop_distance = MathMax(stop_distance, min_stop_distance);
    stop_distance = MathMin(stop_distance, max_stop_distance);

    double stop_loss = entry_price + stop_distance;

    // Calculate take profit levels
    double tp1_distance = stop_distance * TP1_Multiplier;
    double tp2_distance = stop_distance * TP2_Multiplier;
    double take_profit_1 = entry_price - tp1_distance;
    double take_profit_2 = entry_price - tp2_distance;

    // Calculate position size based on confluence score
    double risk_percent = GetRiskPercent(confluence_score);
    double risk_amount = account.Balance() * risk_percent / 100.0;
    double lot_size = risk_amount / (stop_distance / point);

    // Normalize lot size
    double min_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
    double max_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
    double lot_step = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);

    lot_size = MathMax(lot_size, min_lot);
    lot_size = MathMin(lot_size, max_lot);
    lot_size = NormalizeDouble(lot_size / lot_step, 0) * lot_step;

    // Execute the trade
    if(trade.Sell(lot_size, _Symbol, entry_price, stop_loss, 0, TradeComment)) {
        ulong ticket = trade.ResultOrder();

        // Store trade information for management
        TradeInfo new_trade;
        new_trade.ticket = ticket;
        new_trade.entry_price = entry_price;
        new_trade.stop_loss = stop_loss;
        new_trade.take_profit_1 = take_profit_1;
        new_trade.take_profit_2 = take_profit_2;
        new_trade.tp1_hit = false;
        new_trade.tp2_hit = false;
        new_trade.breakeven_moved = false;
        new_trade.original_volume = lot_size;

        AddTradeToArray(new_trade);

        if(EnableLogging) {
            Print("SELL order executed - Ticket: ", ticket,
                  " Confluence: ", confluence_score, "/10",
                  " Lot size: ", lot_size,
                  " Entry: ", entry_price,
                  " SL: ", stop_loss,
                  " TP1: ", take_profit_1,
                  " TP2: ", take_profit_2);
        }

        if(EnableAlerts) {
            Alert("PowerTrade Pro: SELL signal executed with ", confluence_score, "/10 confluence");
        }
    } else {
        if(EnableLogging) Print("Failed to execute SELL order. Error: ", GetLastError());
    }
}

//+------------------------------------------------------------------+
//| Get risk percentage based on confluence score                    |
//+------------------------------------------------------------------+
double GetRiskPercent(int confluence_score)
{
    switch(confluence_score) {
        case 10: return RiskPercent_10_Indicators;
        case 9:  return RiskPercent_9_Indicators;
        case 8:  return RiskPercent_8_Indicators;
        case 7:  return RiskPercent_7_Indicators;
        default: return RiskPercent_7_Indicators;
    }
}

//+------------------------------------------------------------------+
//| Add trade to management array                                    |
//+------------------------------------------------------------------+
void AddTradeToArray(TradeInfo &trade_info)
{
    int size = ArraySize(ActiveTrades);
    ArrayResize(ActiveTrades, size + 1);
    ActiveTrades[size] = trade_info;
}

//+------------------------------------------------------------------+
//| Manage existing trades                                           |
//+------------------------------------------------------------------+
void ManageExistingTrades()
{
    for(int i = ArraySize(ActiveTrades) - 1; i >= 0; i--) {
        if(!position.SelectByTicket(ActiveTrades[i].ticket)) {
            // Position closed, remove from array
            RemoveTradeFromArray(i);
            continue;
        }

        double current_price = position.PriceCurrent();
        double entry_price = ActiveTrades[i].entry_price;
        bool is_buy = (position.PositionType() == POSITION_TYPE_BUY);

        // Check for TP1 hit
        if(!ActiveTrades[i].tp1_hit) {
            bool tp1_reached = false;
            if(is_buy && current_price >= ActiveTrades[i].take_profit_1) {
                tp1_reached = true;
            } else if(!is_buy && current_price <= ActiveTrades[i].take_profit_1) {
                tp1_reached = true;
            }

            if(tp1_reached) {
                // Close 50% of position at TP1
                double close_volume = position.Volume() * TP1_ClosePercent / 100.0;
                close_volume = NormalizeVolume(close_volume);

                if(trade.PositionClosePartial(ActiveTrades[i].ticket, close_volume)) {
                    ActiveTrades[i].tp1_hit = true;

                    // Move stop loss to breakeven
                    if(!ActiveTrades[i].breakeven_moved) {
                        trade.PositionModify(ActiveTrades[i].ticket, entry_price, 0);
                        ActiveTrades[i].breakeven_moved = true;
                    }

                    if(EnableLogging) {
                        Print("TP1 hit for ticket ", ActiveTrades[i].ticket,
                              " - Closed ", close_volume, " lots, moved SL to breakeven");
                    }
                }
            }
        }

        // Check for TP2 hit
        if(ActiveTrades[i].tp1_hit && !ActiveTrades[i].tp2_hit) {
            bool tp2_reached = false;
            if(is_buy && current_price >= ActiveTrades[i].take_profit_2) {
                tp2_reached = true;
            } else if(!is_buy && current_price <= ActiveTrades[i].take_profit_2) {
                tp2_reached = true;
            }

            if(tp2_reached) {
                // Close 30% of original position at TP2
                double close_volume = ActiveTrades[i].original_volume * TP2_ClosePercent / 100.0;
                close_volume = NormalizeVolume(close_volume);
                close_volume = MathMin(close_volume, position.Volume());

                if(trade.PositionClosePartial(ActiveTrades[i].ticket, close_volume)) {
                    ActiveTrades[i].tp2_hit = true;

                    if(EnableLogging) {
                        Print("TP2 hit for ticket ", ActiveTrades[i].ticket,
                              " - Closed ", close_volume, " lots, trailing remaining position");
                    }
                }
            }
        }

        // Apply trailing stop using Parabolic SAR for remaining position
        if(ActiveTrades[i].tp2_hit) {
            double new_stop = 0;

            if(is_buy) {
                new_stop = SAR[0];
                if(new_stop > position.StopLoss() && new_stop < current_price) {
                    trade.PositionModify(ActiveTrades[i].ticket, new_stop, 0);
                    if(EnableLogging) {
                        Print("Trailing stop updated for BUY ticket ", ActiveTrades[i].ticket,
                              " - New SL: ", new_stop);
                    }
                }
            } else {
                new_stop = SAR[0];
                if(new_stop < position.StopLoss() && new_stop > current_price) {
                    trade.PositionModify(ActiveTrades[i].ticket, new_stop, 0);
                    if(EnableLogging) {
                        Print("Trailing stop updated for SELL ticket ", ActiveTrades[i].ticket,
                              " - New SL: ", new_stop);
                    }
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Remove trade from management array                               |
//+------------------------------------------------------------------+
void RemoveTradeFromArray(int index)
{
    int size = ArraySize(ActiveTrades);
    if(index < 0 || index >= size) return;

    for(int i = index; i < size - 1; i++) {
        ActiveTrades[i] = ActiveTrades[i + 1];
    }

    ArrayResize(ActiveTrades, size - 1);
}

//+------------------------------------------------------------------+
//| Normalize volume to valid lot size                               |
//+------------------------------------------------------------------+
double NormalizeVolume(double volume)
{
    double min_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
    double max_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
    double lot_step = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);

    volume = MathMax(volume, min_lot);
    volume = MathMin(volume, max_lot);
    volume = NormalizeDouble(volume / lot_step, 0) * lot_step;

    return volume;
}

//+------------------------------------------------------------------+
//| Expert advisor event handlers                                    |
//+------------------------------------------------------------------+
void OnTradeTransaction(const MqlTradeTransaction& trans,
                       const MqlTradeRequest& request,
                       const MqlTradeResult& result)
{
    // Handle trade events if needed
    if(trans.symbol == _Symbol &&
       (trans.type == TRADE_TRANSACTION_DEAL_ADD || trans.type == TRADE_TRANSACTION_DEAL_DELETE)) {

        if(EnableLogging) {
            Print("Trade transaction: ", EnumToString(trans.type),
                  " Ticket: ", trans.order,
                  " Volume: ", trans.volume,
                  " Price: ", trans.price);
        }
    }
}

//+------------------------------------------------------------------+
//| Chart event handler                                              |
//+------------------------------------------------------------------+
void OnChartEvent(const int id,
                 const long &lparam,
                 const double &dparam,
                 const string &sparam)
{
    // Handle chart events if needed
    // Can be used for manual trade management interface
}

//+------------------------------------------------------------------+
//| Timer event handler                                              |
//+------------------------------------------------------------------+
void OnTimer()
{
    // Periodic checks can be implemented here
    // For example, checking for news events or market conditions
}

//+------------------------------------------------------------------+
//| Utility function to log trade statistics                         |
//+------------------------------------------------------------------+
void LogTradeStatistics()
{
    if(!EnableLogging) return;

    int total_trades = 0;
    double total_profit = 0;
    int winning_trades = 0;

    // Calculate statistics from history
    HistorySelect(0, TimeCurrent());

    for(int i = HistoryDealsTotal() - 1; i >= 0; i--) {
        ulong ticket = HistoryDealGetTicket(i);
        if(HistoryDealGetString(ticket, DEAL_SYMBOL) == _Symbol &&
           HistoryDealGetInteger(ticket, DEAL_MAGIC) == MagicNumber &&
           HistoryDealGetInteger(ticket, DEAL_ENTRY) == DEAL_ENTRY_OUT) {

            total_trades++;
            double profit = HistoryDealGetDouble(ticket, DEAL_PROFIT);
            total_profit += profit;

            if(profit > 0) winning_trades++;
        }
    }

    if(total_trades > 0) {
        double win_rate = (double)winning_trades / total_trades * 100;
        Print("=== PowerTrade Pro Statistics ===");
        Print("Total trades: ", total_trades);
        Print("Winning trades: ", winning_trades);
        Print("Win rate: ", DoubleToString(win_rate, 2), "%");
        Print("Total profit: ", DoubleToString(total_profit, 2));
        Print("Average profit per trade: ", DoubleToString(total_profit / total_trades, 2));
        Print("================================");
    }
}

//+------------------------------------------------------------------+
//| Check if it's news time (simplified implementation)              |
//+------------------------------------------------------------------+
bool IsNewsTime()
{
    // This is a simplified news filter
    // In a real implementation, you would integrate with a news calendar API
    // For now, we'll avoid trading during typical high-impact news times

    MqlDateTime dt;
    TimeToStruct(TimeCurrent(), dt);

    // Avoid trading during typical news release times (GMT)
    // Major news usually released at: 8:30, 10:00, 12:30, 14:00, 15:30
    int current_minute = dt.hour * 60 + dt.min;

    int news_times[] = {830, 1000, 1230, 1400, 1530}; // Times in minutes from midnight

    for(int i = 0; i < ArraySize(news_times); i++) {
        if(MathAbs(current_minute - news_times[i]) <= NewsAvoidMinutes) {
            return true;
        }
    }

    return false;
}

//+------------------------------------------------------------------+
//| Enhanced signal validation                                       |
//+------------------------------------------------------------------+
bool ValidateSignal(int confluence_score, bool is_buy)
{
    // Additional signal validation checks

    // 1. Check minimum confluence requirement
    if(confluence_score < MinConfluence) {
        return false;
    }

    // 2. Check volatility conditions
    double current_atr = ATR[0];
    double atr_average = 0;
    double atr_buffer[30];

    if(CopyBuffer(handle_ATR, 0, 1, 30, atr_buffer) > 0) {
        for(int i = 0; i < 30; i++) {
            atr_average += atr_buffer[i];
        }
        atr_average /= 30;

        // Avoid trading in extremely high volatility
        if(current_atr > atr_average * 2.0) {
            if(EnableLogging) Print("Volatility too high - ATR: ", current_atr, " vs Average: ", atr_average);
            return false;
        }

        // Avoid trading in extremely low volatility
        if(current_atr < atr_average * 0.5) {
            if(EnableLogging) Print("Volatility too low - ATR: ", current_atr, " vs Average: ", atr_average);
            return false;
        }
    }

    // 3. Check spread conditions
    double spread = SymbolInfoInteger(_Symbol, SYMBOL_SPREAD) * SymbolInfoDouble(_Symbol, SYMBOL_POINT);
    double max_spread = current_atr * 0.1; // Maximum spread should be 10% of ATR

    if(spread > max_spread) {
        if(EnableLogging) Print("Spread too wide: ", spread, " vs Max allowed: ", max_spread);
        return false;
    }

    return true;
}

//+------------------------------------------------------------------+
//| Emergency stop function                                          |
//+------------------------------------------------------------------+
void EmergencyStop(string reason)
{
    if(EnableLogging) Print("EMERGENCY STOP TRIGGERED: ", reason);
    if(EnableAlerts) Alert("PowerTrade Pro EMERGENCY STOP: ", reason);

    // Close all positions managed by this EA
    for(int i = PositionsTotal() - 1; i >= 0; i--) {
        if(position.SelectByIndex(i)) {
            if(position.Symbol() == _Symbol && position.Magic() == MagicNumber) {
                trade.PositionClose(position.Ticket());
                if(EnableLogging) Print("Emergency closed position: ", position.Ticket());
            }
        }
    }

    // Clear active trades array
    ArrayResize(ActiveTrades, 0);
}
