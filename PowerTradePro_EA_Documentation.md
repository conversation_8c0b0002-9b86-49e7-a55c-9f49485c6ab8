# PowerTrade Pro Expert Advisor - Documentation

## Overview
PowerTrade Pro is a sophisticated Expert Advisor for MetaTrader 5 that implements a 10-indicator confluence trading strategy. The EA requires a minimum of 7 out of 10 indicators to align before executing trades, ensuring high-probability setups with a target win rate of 65-75%.

## Key Features

### 10-Indicator Confluence System
1. **EMA Ribbon (20, 50, 100)** - Trend identification
2. **Ichimoku Cloud** - Trend confirmation and support/resistance
3. **Parabolic SAR** - Trend direction and trailing stops
4. **RSI (14)** - Momentum and overbought/oversold conditions
5. **MACD (12, 26, 9)** - Trend momentum and divergence
6. **Stochastic Oscillator (14, 3, 3)** - Entry timing
7. **Bollinger Bands (20, 2)** - Volatility measurement
8. **ATR (14)** - Volatility-based position sizing
9. **VWAP** - Institutional price levels
10. **OBV** - Volume flow confirmation

### Advanced Risk Management
- **Dynamic Position Sizing**: Based on confluence score (7-10 indicators)
  - 7 indicators: 1.0% risk
  - 8 indicators: 1.5% risk
  - 9 indicators: 2.0% risk
  - 10 indicators: 2.5% risk
- **ATR-Based Stop Losses**: Adaptive to market volatility
- **Multi-Level Take Profits**: 50% at TP1, 30% at TP2, 20% trailing
- **Daily/Weekly/Monthly Loss Limits**: 6%/12%/20% respectively
- **Maximum Concurrent Trades**: 3 positions

### Trade Management Features
- **Partial Profit Taking**: Systematic position scaling
- **Breakeven Protection**: Move SL to entry after TP1
- **Trailing Stops**: Using Parabolic SAR for final position
- **Emergency Stop**: Automatic shutdown on excessive losses

### Safety Features
- **Session Filtering**: Trade only during optimal hours (12:00-16:00 GMT)
- **News Avoidance**: Skip trading during high-impact news times
- **Volatility Filters**: Avoid extreme market conditions
- **Spread Protection**: Maximum spread limits based on ATR
- **Performance Monitoring**: Automatic shutdown if win rate drops below 55%

## Installation Instructions

1. **Copy the EA file** to your MetaTrader 5 data folder:
   ```
   MQL5/Experts/PowerTradePro_EA.mq5
   ```

2. **Compile the EA** in MetaEditor or restart MT5 to auto-compile

3. **Attach to Chart**:
   - Open your preferred currency pair (EUR/USD, GBP/USD, USD/JPY recommended)
   - Use H1 (1-hour) timeframe for optimal performance
   - Drag the EA from Navigator to the chart
   - Configure parameters as needed

## Parameter Configuration

### Strategy Settings
- **MinConfluence**: Minimum indicators required (7-10) - Default: 7
- **EnableBuySignals**: Enable long positions - Default: true
- **EnableSellSignals**: Enable short positions - Default: true

### Risk Management
- **RiskPercent_X_Indicators**: Risk percentage for each confluence level
- **MaxDailyLoss**: Maximum daily loss percentage - Default: 6%
- **MaxWeeklyLoss**: Maximum weekly loss percentage - Default: 12%
- **MaxMonthlyLoss**: Maximum monthly loss percentage - Default: 20%
- **MaxConcurrentTrades**: Maximum simultaneous positions - Default: 3

### Stop Loss & Take Profit
- **ATR_Multiplier**: ATR multiplier for stop loss - Default: 2.0
- **MinStopLoss**: Minimum stop loss in pips - Default: 20
- **MaxStopLoss**: Maximum stop loss in pips - Default: 100
- **TP1_Multiplier**: First take profit multiplier - Default: 1.5
- **TP2_Multiplier**: Second take profit multiplier - Default: 2.5

### Session Filters
- **EnableSessionFilter**: Enable trading session filtering - Default: true
- **SessionStartHour**: Session start hour GMT - Default: 12
- **SessionEndHour**: Session end hour GMT - Default: 16
- **AvoidNews**: Avoid trading during news - Default: true
- **NewsAvoidMinutes**: Minutes to avoid before/after news - Default: 30

## Signal Generation Logic

### BUY Signal Requirements
**Primary Conditions (All 4 Required):**
1. EMA20 > EMA50 > EMA100
2. Price above Ichimoku cloud AND Tenkan > Kijun
3. Price above VWAP
4. Parabolic SAR dots below price

**Secondary Conditions (3 of 6 Required):**
5. RSI > 50 and rising (not overbought)
6. MACD line above signal AND above zero
7. Stochastic %K crosses above %D or both below 50
8. Price in upper half of Bollinger Bands OR breakout above upper band
9. Volume above 20-period average
10. OBV rising or making higher highs

### SELL Signal Requirements
**Primary Conditions (All 4 Required):**
1. EMA20 < EMA50 < EMA100
2. Price below Ichimoku cloud AND Tenkan < Kijun
3. Price below VWAP
4. Parabolic SAR dots above price

**Secondary Conditions (3 of 6 Required):**
5. RSI < 50 and falling (not oversold)
6. MACD line below signal AND below zero
7. Stochastic %K crosses below %D or both above 50
8. Price in lower half of Bollinger Bands OR breakout below lower band
9. Volume above 20-period average
10. OBV falling or making lower lows

## Trade Management Process

1. **Entry**: Execute trade when confluence score ≥ 7 and all conditions met
2. **TP1 Hit**: Close 50% of position, move stop loss to breakeven
3. **TP2 Hit**: Close 30% of position, begin trailing with Parabolic SAR
4. **Final 20%**: Trail with SAR until stopped out or manually closed

## Performance Expectations

- **Target Win Rate**: 65-75%
- **Risk/Reward Ratio**: 1:2 average
- **Monthly Return**: 8-15%
- **Maximum Drawdown**: 12-18%
- **Trade Frequency**: 2-5 signals per day
- **Average Trade Duration**: 8-24 hours

## Recommended Currency Pairs

### Tier 1 (Primary Focus)
- EUR/USD
- GBP/USD
- USD/JPY
- AUD/USD

### Tier 2 (Secondary)
- USD/CAD
- NZD/USD
- EUR/GBP
- GBP/JPY

### Commodities
- XAU/USD (Gold) - Reduce position size due to higher volatility

## Troubleshooting

### Common Issues
1. **No Trades Executing**: Check confluence requirements, session filters, and risk limits
2. **High Spread Errors**: Adjust spread filter or trade during better market conditions
3. **Frequent Stops**: Consider increasing ATR multiplier or checking market conditions
4. **Low Win Rate**: Review confluence settings and market conditions

### Log Messages
- Monitor the Experts tab for detailed logging information
- Enable logging in parameters for comprehensive trade analysis
- Check for emergency stop triggers and risk limit violations

## Optimization Tips

1. **Backtest Thoroughly**: Test on historical data before live trading
2. **Start Small**: Begin with minimum risk settings
3. **Monitor Performance**: Review statistics regularly
4. **Adapt to Market**: Consider seasonal adjustments
5. **Keep Records**: Maintain detailed trading journal

## Disclaimer

This Expert Advisor is for educational and research purposes. Past performance does not guarantee future results. Always test thoroughly on demo accounts before live trading. Trading involves substantial risk of loss and is not suitable for all investors.

## Support

For questions or issues, refer to the EA code comments or consult with a qualified MQL5 developer.

---

**Version**: 1.00  
**Last Updated**: 2024  
**Compatible with**: MetaTrader 5 Build 3815+
