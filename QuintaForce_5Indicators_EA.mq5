//+------------------------------------------------------------------+
//|                                    QuintaForce_5Indicators_EA.mq5 |
//|                                  Copyright 2024, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, MetaQuotes Ltd."
#property link      "https://www.mql5.com"
#property version   "1.00"

#include <Trade\Trade.mqh>

//--- Input parameters
input group "=== Strategy Settings ==="
input double   InpRiskPercent = 2.0;           // Risk per trade (%)
input double   InpMinRiskReward = 2.0;         // Minimum Risk/Reward ratio
input int      InpMagicNumber = 123456;        // Magic number
input string   InpComment = "QuintaForce";     // Trade comment

input group "=== RSI Settings ==="
input int      InpRSIPeriod = 14;              // RSI Period
input double   InpRSIOverbought = 70.0;        // RSI Overbought level
input double   InpRSIOversold = 30.0;          // RSI Oversold level
input double   InpRSIBuyMin = 40.0;            // RSI Buy minimum
input double   InpRSIBuyMax = 65.0;            // RSI Buy maximum
input double   InpRSISellMin = 35.0;           // RSI Sell minimum
input double   InpRSISellMax = 60.0;           // RSI Sell maximum

input group "=== MACD Settings ==="
input int      InpMACDFast = 12;               // MACD Fast EMA
input int      InpMACDSlow = 26;               // MACD Slow EMA
input int      InpMACDSignal = 9;              // MACD Signal period

input group "=== Bollinger Bands Settings ==="
input int      InpBBPeriod = 20;               // Bollinger Bands period
input double   InpBBDeviation = 2.0;           // Bollinger Bands deviation

input group "=== EMA Settings ==="
input int      InpEMAFast = 8;                 // Fast EMA period
input int      InpEMASlow = 21;                // Slow EMA period

input group "=== VWAP Settings ==="
input bool     InpUseVWAP = true;              // Use VWAP filter
input int      InpVWAPDistance = 20;           // VWAP distance in points

input group "=== Risk Management ==="
input int      InpATRPeriod = 14;              // ATR period for stops
input double   InpATRMultiplier = 1.5;         // ATR multiplier for stops
input int      InpMinStopLoss = 25;            // Minimum stop loss (pips)
input int      InpMaxStopLoss = 60;            // Maximum stop loss (pips)
input int      InpMaxHoldHours = 24;           // Maximum hold time (hours)

input group "=== Take Profit Settings ==="
input double   InpTP1Ratio = 1.5;              // TP1 ratio (% of SL)
input double   InpTP2Ratio = 2.5;              // TP2 ratio (% of SL)
input double   InpTP1Volume = 50.0;            // TP1 volume (%)
input double   InpTP2Volume = 30.0;            // TP2 volume (%)

//--- Global variables
CTrade trade;
int rsiHandle, macdHandle, bbHandle, emaFastHandle, emaSlowHandle, atrHandle;
double rsiBuffer[], macdMainBuffer[], macdSignalBuffer[], bbUpperBuffer[], bbMiddleBuffer[], bbLowerBuffer[];
double emaFastBuffer[], emaSlowBuffer[], atrBuffer[];
datetime lastTradeTime = 0;
bool tradeAllowed = true;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    // Initialize trade object
    trade.SetExpertMagicNumber(InpMagicNumber);
    trade.SetMarginMode();
    trade.SetTypeFillingBySymbol(Symbol());
    
    // Create indicator handles
    rsiHandle = iRSI(Symbol(), PERIOD_H1, InpRSIPeriod, PRICE_CLOSE);
    macdHandle = iMACD(Symbol(), PERIOD_H1, InpMACDFast, InpMACDSlow, InpMACDSignal, PRICE_CLOSE);
    bbHandle = iBands(Symbol(), PERIOD_H1, InpBBPeriod, 0, InpBBDeviation, PRICE_CLOSE);
    emaFastHandle = iMA(Symbol(), PERIOD_H1, InpEMAFast, 0, MODE_EMA, PRICE_CLOSE);
    emaSlowHandle = iMA(Symbol(), PERIOD_H1, InpEMASlow, 0, MODE_EMA, PRICE_CLOSE);
    atrHandle = iATR(Symbol(), PERIOD_H1, InpATRPeriod);
    
    // Check if handles are valid
    if(rsiHandle == INVALID_HANDLE || macdHandle == INVALID_HANDLE || bbHandle == INVALID_HANDLE ||
       emaFastHandle == INVALID_HANDLE || emaSlowHandle == INVALID_HANDLE || atrHandle == INVALID_HANDLE)
    {
        Print("Error creating indicator handles");
        return INIT_FAILED;
    }
    
    // Resize arrays
    ArraySetAsSeries(rsiBuffer, true);
    ArraySetAsSeries(macdMainBuffer, true);
    ArraySetAsSeries(macdSignalBuffer, true);
    ArraySetAsSeries(bbUpperBuffer, true);
    ArraySetAsSeries(bbMiddleBuffer, true);
    ArraySetAsSeries(bbLowerBuffer, true);
    ArraySetAsSeries(emaFastBuffer, true);
    ArraySetAsSeries(emaSlowBuffer, true);
    ArraySetAsSeries(atrBuffer, true);
    
    Print("QuintaForce 5-Indicators EA initialized successfully");
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    // Release indicator handles
    IndicatorRelease(rsiHandle);
    IndicatorRelease(macdHandle);
    IndicatorRelease(bbHandle);
    IndicatorRelease(emaFastHandle);
    IndicatorRelease(emaSlowHandle);
    IndicatorRelease(atrHandle);
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // Check if new bar formed
    static datetime lastBarTime = 0;
    datetime currentBarTime = iTime(Symbol(), PERIOD_H1, 0);
    
    if(currentBarTime == lastBarTime)
        return;
    
    lastBarTime = currentBarTime;
    
    // Update indicator buffers
    if(!UpdateIndicators())
        return;
    
    // Check for trade signals
    CheckTradeSignals();
    
    // Manage existing positions
    ManagePositions();
}

//+------------------------------------------------------------------+
//| Update all indicator buffers                                     |
//+------------------------------------------------------------------+
bool UpdateIndicators()
{
    // Copy indicator values
    if(CopyBuffer(rsiHandle, 0, 0, 3, rsiBuffer) <= 0) return false;
    if(CopyBuffer(macdHandle, MAIN_LINE, 0, 3, macdMainBuffer) <= 0) return false;
    if(CopyBuffer(macdHandle, SIGNAL_LINE, 0, 3, macdSignalBuffer) <= 0) return false;
    if(CopyBuffer(bbHandle, UPPER_BAND, 0, 3, bbUpperBuffer) <= 0) return false;
    if(CopyBuffer(bbHandle, BASE_LINE, 0, 3, bbMiddleBuffer) <= 0) return false;
    if(CopyBuffer(bbHandle, LOWER_BAND, 0, 3, bbLowerBuffer) <= 0) return false;
    if(CopyBuffer(emaFastHandle, 0, 0, 3, emaFastBuffer) <= 0) return false;
    if(CopyBuffer(emaSlowHandle, 0, 0, 3, emaSlowBuffer) <= 0) return false;
    if(CopyBuffer(atrHandle, 0, 0, 3, atrBuffer) <= 0) return false;
    
    return true;
}

//+------------------------------------------------------------------+
//| Check for trade signals                                          |
//+------------------------------------------------------------------+
void CheckTradeSignals()
{
    // Don't trade if position already exists
    if(PositionsTotal() > 0)
        return;
    
    // Check buy signal
    if(IsBuySignal())
    {
        OpenBuyTrade();
    }
    // Check sell signal
    else if(IsSellSignal())
    {
        OpenSellTrade();
    }
}

//+------------------------------------------------------------------+
//| Check buy signal conditions                                      |
//+------------------------------------------------------------------+
bool IsBuySignal()
{
    double currentPrice = SymbolInfoDouble(Symbol(), SYMBOL_BID);
    int signalCount = 0;
    
    // 1. EMA Cross - EMA8 > EMA21
    if(emaFastBuffer[0] > emaSlowBuffer[0])
        signalCount++;
    
    // 2. MACD - MACD line > Signal line
    if(macdMainBuffer[0] > macdSignalBuffer[0])
        signalCount++;
    
    // 3. VWAP - Price above VWAP (using middle BB as proxy)
    if(currentPrice > bbMiddleBuffer[0])
        signalCount++;
    
    // 4. Bollinger Bands - Price in upper half or above middle
    if(currentPrice >= bbMiddleBuffer[0])
        signalCount++;
    
    // Need at least 4 out of 4 primary conditions
    if(signalCount < 4)
        return false;
    
    // 5. RSI Confirmation - Between 40-65
    if(rsiBuffer[0] < InpRSIBuyMin || rsiBuffer[0] > InpRSIBuyMax)
        return false;
    
    return true;
}

//+------------------------------------------------------------------+
//| Check sell signal conditions                                     |
//+------------------------------------------------------------------+
bool IsSellSignal()
{
    double currentPrice = SymbolInfoDouble(Symbol(), SYMBOL_ASK);
    int signalCount = 0;
    
    // 1. EMA Cross - EMA8 < EMA21
    if(emaFastBuffer[0] < emaSlowBuffer[0])
        signalCount++;
    
    // 2. MACD - MACD line < Signal line
    if(macdMainBuffer[0] < macdSignalBuffer[0])
        signalCount++;
    
    // 3. VWAP - Price below VWAP (using middle BB as proxy)
    if(currentPrice < bbMiddleBuffer[0])
        signalCount++;
    
    // 4. Bollinger Bands - Price in lower half or below middle
    if(currentPrice <= bbMiddleBuffer[0])
        signalCount++;
    
    // Need at least 4 out of 4 primary conditions
    if(signalCount < 4)
        return false;
    
    // 5. RSI Confirmation - Between 35-60
    if(rsiBuffer[0] < InpRSISellMin || rsiBuffer[0] > InpRSISellMax)
        return false;
    
    return true;
}

//+------------------------------------------------------------------+
//| Open buy trade                                                   |
//+------------------------------------------------------------------+
void OpenBuyTrade()
{
    double ask = SymbolInfoDouble(Symbol(), SYMBOL_ASK);
    double atr = atrBuffer[0];

    // Calculate stop loss
    double stopLoss = CalculateStopLoss(ORDER_TYPE_BUY, ask, atr);
    double stopDistance = ask - stopLoss;

    // Calculate position size based on risk
    double lotSize = CalculateLotSize(stopDistance);

    // Calculate take profit levels
    double tp1 = ask + (stopDistance * InpTP1Ratio);
    double tp2 = ask + (stopDistance * InpTP2Ratio);

    // Open main position
    if(trade.Buy(lotSize, Symbol(), ask, stopLoss, tp1, InpComment))
    {
        Print("Buy order opened: Price=", ask, " SL=", stopLoss, " TP=", tp1);
        lastTradeTime = TimeCurrent();
    }
}

//+------------------------------------------------------------------+
//| Open sell trade                                                  |
//+------------------------------------------------------------------+
void OpenSellTrade()
{
    double bid = SymbolInfoDouble(Symbol(), SYMBOL_BID);
    double atr = atrBuffer[0];

    // Calculate stop loss
    double stopLoss = CalculateStopLoss(ORDER_TYPE_SELL, bid, atr);
    double stopDistance = stopLoss - bid;

    // Calculate position size based on risk
    double lotSize = CalculateLotSize(stopDistance);

    // Calculate take profit levels
    double tp1 = bid - (stopDistance * InpTP1Ratio);
    double tp2 = bid - (stopDistance * InpTP2Ratio);

    // Open main position
    if(trade.Sell(lotSize, Symbol(), bid, stopLoss, tp1, InpComment))
    {
        Print("Sell order opened: Price=", bid, " SL=", stopLoss, " TP=", tp1);
        lastTradeTime = TimeCurrent();
    }
}

//+------------------------------------------------------------------+
//| Calculate stop loss based on ATR                                 |
//+------------------------------------------------------------------+
double CalculateStopLoss(ENUM_ORDER_TYPE orderType, double entryPrice, double atr)
{
    double stopDistance = atr * InpATRMultiplier;
    double minStop = InpMinStopLoss * Point() * 10; // Convert pips to points
    double maxStop = InpMaxStopLoss * Point() * 10;

    // Ensure stop distance is within limits
    if(stopDistance < minStop) stopDistance = minStop;
    if(stopDistance > maxStop) stopDistance = maxStop;

    double stopLoss;
    if(orderType == ORDER_TYPE_BUY)
        stopLoss = entryPrice - stopDistance;
    else
        stopLoss = entryPrice + stopDistance;

    return NormalizeDouble(stopLoss, Digits());
}

//+------------------------------------------------------------------+
//| Calculate lot size based on risk percentage                      |
//+------------------------------------------------------------------+
double CalculateLotSize(double stopDistance)
{
    double accountBalance = AccountInfoDouble(ACCOUNT_BALANCE);
    double riskAmount = accountBalance * InpRiskPercent / 100.0;

    double tickValue = SymbolInfoDouble(Symbol(), SYMBOL_TRADE_TICK_VALUE);
    double tickSize = SymbolInfoDouble(Symbol(), SYMBOL_TRADE_TICK_SIZE);

    double lotSize = riskAmount / (stopDistance / tickSize * tickValue);

    // Normalize lot size
    double minLot = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MIN);
    double maxLot = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MAX);
    double lotStep = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_STEP);

    lotSize = MathMax(minLot, MathMin(maxLot, lotSize));
    lotSize = NormalizeDouble(lotSize / lotStep, 0) * lotStep;

    return lotSize;
}

//+------------------------------------------------------------------+
//| Manage existing positions                                        |
//+------------------------------------------------------------------+
void ManagePositions()
{
    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        if(PositionSelectByIndex(i))
        {
            if(PositionGetString(POSITION_SYMBOL) != Symbol() ||
               PositionGetInteger(POSITION_MAGIC) != InpMagicNumber)
                continue;

            // Check maximum hold time
            datetime openTime = (datetime)PositionGetInteger(POSITION_TIME);
            if(TimeCurrent() - openTime > InpMaxHoldHours * 3600)
            {
                trade.PositionClose(PositionGetTicket(i));
                Print("Position closed due to maximum hold time");
                continue;
            }

            // Check for indicator reversal
            if(CheckIndicatorReversal())
            {
                trade.PositionClose(PositionGetTicket(i));
                Print("Position closed due to indicator reversal");
                continue;
            }

            // Move stop to breakeven at TP1
            MoveStopToBreakeven(i);
        }
    }
}

//+------------------------------------------------------------------+
//| Check for indicator reversal                                     |
//+------------------------------------------------------------------+
bool CheckIndicatorReversal()
{
    // Count how many indicators are against current position
    int againstCount = 0;

    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        if(PositionSelectByIndex(i))
        {
            if(PositionGetString(POSITION_SYMBOL) != Symbol() ||
               PositionGetInteger(POSITION_MAGIC) != InpMagicNumber)
                continue;

            ENUM_POSITION_TYPE posType = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
            double currentPrice = (posType == POSITION_TYPE_BUY) ?
                                 SymbolInfoDouble(Symbol(), SYMBOL_BID) :
                                 SymbolInfoDouble(Symbol(), SYMBOL_ASK);

            if(posType == POSITION_TYPE_BUY)
            {
                // Check indicators against buy position
                if(emaFastBuffer[0] < emaSlowBuffer[0]) againstCount++;
                if(macdMainBuffer[0] < macdSignalBuffer[0]) againstCount++;
                if(currentPrice < bbMiddleBuffer[0]) againstCount++;
                if(rsiBuffer[0] > InpRSIOverbought) againstCount++;
            }
            else
            {
                // Check indicators against sell position
                if(emaFastBuffer[0] > emaSlowBuffer[0]) againstCount++;
                if(macdMainBuffer[0] > macdSignalBuffer[0]) againstCount++;
                if(currentPrice > bbMiddleBuffer[0]) againstCount++;
                if(rsiBuffer[0] < InpRSIOversold) againstCount++;
            }
        }
    }

    // Exit if 3 or more indicators are against position
    return (againstCount >= 3);
}

//+------------------------------------------------------------------+
//| Move stop loss to breakeven                                      |
//+------------------------------------------------------------------+
void MoveStopToBreakeven(int posIndex)
{
    if(!PositionSelectByIndex(posIndex))
        return;

    double openPrice = PositionGetDouble(POSITION_PRICE_OPEN);
    double currentSL = PositionGetDouble(POSITION_SL);
    double currentTP = PositionGetDouble(POSITION_TP);
    ENUM_POSITION_TYPE posType = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
    double currentPrice = (posType == POSITION_TYPE_BUY) ?
                         SymbolInfoDouble(Symbol(), SYMBOL_BID) :
                         SymbolInfoDouble(Symbol(), SYMBOL_ASK);

    // Check if price has moved enough to move SL to breakeven
    double moveDistance = MathAbs(currentTP - openPrice) * 0.5; // 50% of TP distance

    bool shouldMoveToBE = false;
    if(posType == POSITION_TYPE_BUY && currentPrice >= openPrice + moveDistance)
        shouldMoveToBE = true;
    else if(posType == POSITION_TYPE_SELL && currentPrice <= openPrice - moveDistance)
        shouldMoveToBE = true;

    if(shouldMoveToBE)
    {
        // Only move if current SL is not already at or better than breakeven
        if((posType == POSITION_TYPE_BUY && currentSL < openPrice) ||
           (posType == POSITION_TYPE_SELL && currentSL > openPrice))
        {
            trade.PositionModify(PositionGetTicket(posIndex), openPrice, currentTP);
            Print("Stop loss moved to breakeven for position ", PositionGetTicket(posIndex));
        }
    }
}
