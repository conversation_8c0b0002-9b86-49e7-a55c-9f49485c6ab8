//+------------------------------------------------------------------+
//|                                     VolatilityBreakoutTrend.mq5  |
//|                        Copyright 2024, MetaQuotes Software Corp. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024"
#property version   "1.00"
#property strict

// Input parameters
input double   RiskPercent      = 1.0;     // Risk per trade (%)
input int      ATR_Period       = 14;      // ATR Period
input double   ATR_Multiplier   = 1.5;     // ATR Multiplier for SL
input double   RR_Ratio         = 2.0;     // Risk:Reward Ratio
input int      EMA_Fast         = 50;      // Fast EMA
input int      EMA_Slow         = 200;     // Slow EMA
input int      Min_ATR_Pips     = 15;      // Min Volatility (pips)
input int      Max_Trades_Day   = 3;       // Max Trades/Day
input bool     UseTrailingStop  = true;    // Use Trailing Stop
input double   TrailingStart    = 20;      // Trailing Start (pips)
input double   TrailingStep     = 10;      // Trailing Step (pips)
input bool     UseBreakEven     = true;    // Use Break Even
input double   BreakEvenPips    = 15;      // Break Even Trigger (pips)
input int      MagicNumber      = 123456;  // Magic Number
input string   TradeComment     = "VBT_EA"; // Trade Comment

// Global variables
datetime lastTradeTime;
int tradesToday;
int atrHandle;
int emaFastHandle;
int emaSlowHandle;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   // Initialize handles
   atrHandle = iATR(_Symbol, PERIOD_D1, ATR_Period);
   emaFastHandle = iMA(_Symbol, PERIOD_H4, EMA_Fast, 0, MODE_EMA, PRICE_CLOSE);
   emaSlowHandle = iMA(_Symbol, PERIOD_H4, EMA_Slow, 0, MODE_EMA, PRICE_CLOSE);
   
   // Check if handles are valid
   if(atrHandle == INVALID_HANDLE || emaFastHandle == INVALID_HANDLE || emaSlowHandle == INVALID_HANDLE)
   {
      Print("Error creating indicators");
      return(INIT_FAILED);
   }
   
   lastTradeTime = 0;
   tradesToday = 0;
   
   Print("VolatilityBreakoutTrend EA initialized successfully");
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   // Release indicator handles
   if(atrHandle != INVALID_HANDLE) IndicatorRelease(atrHandle);
   if(emaFastHandle != INVALID_HANDLE) IndicatorRelease(emaFastHandle);
   if(emaSlowHandle != INVALID_HANDLE) IndicatorRelease(emaSlowHandle);
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
   // Reset daily trade counter
   if (TimeDay(TimeCurrent()) != TimeDay(lastTradeTime))
      tradesToday = 0;

   // Check if trading is allowed
   if(!IsTradingAllowed())
      return;

   // Manage existing positions
   ManagePositions();

   // Check if we already have open positions (don't open new ones if we have existing)
   if(HasOpenPositions())
      return;

   // Check trading conditions
   if (tradesToday < Max_Trades_Day && IsNewBar())
   {
      double atr[];
      if(CopyBuffer(atrHandle, 0, 0, 1, atr) <= 0)
      {
         Print("Error copying ATR data");
         return;
      }

      double minVolatility = Min_ATR_Pips * _Point;

      if (atr[0] > minVolatility)
      {
         // Check spread condition
         double currentSpread = GetSpread();
         if(currentSpread > Min_ATR_Pips * 0.5) // Don't trade if spread is too high
         {
            Print("Spread too high: ", currentSpread, " points");
            return;
         }

         // Get EMA values
         double fastEMA[], slowEMA[];
         if(CopyBuffer(emaFastHandle, 0, 0, 1, fastEMA) <= 0 ||
            CopyBuffer(emaSlowHandle, 0, 0, 1, slowEMA) <= 0)
         {
            Print("Error copying EMA data");
            return;
         }

         // Entry logic
         if (fastEMA[0] > slowEMA[0] && IsBullishBreak())
            PlaceTrade(ORDER_TYPE_BUY, atr[0]);
         else if (fastEMA[0] < slowEMA[0] && IsBearishBreak())
            PlaceTrade(ORDER_TYPE_SELL, atr[0]);
      }
   }
}

//+------------------------------------------------------------------+
//| Check for bullish breakout condition                             |
//+------------------------------------------------------------------+
bool IsBullishBreak()
{
   double prevHigh = iHigh(_Symbol, PERIOD_D1, 1);
   double currentAsk = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
   return (currentAsk > prevHigh);
}

//+------------------------------------------------------------------+
//| Check for bearish breakout condition                             |
//+------------------------------------------------------------------+
bool IsBearishBreak()
{
   double prevLow = iLow(_Symbol, PERIOD_D1, 1);
   double currentBid = SymbolInfoDouble(_Symbol, SYMBOL_BID);
   return (currentBid < prevLow);
}

//+------------------------------------------------------------------+
//| Execute trade with risk management                               |
//+------------------------------------------------------------------+
void PlaceTrade(ENUM_ORDER_TYPE type, double atr)
{
   double sl = atr * ATR_Multiplier;
   double tp = sl * RR_Ratio;
   double entry = (type == ORDER_TYPE_BUY) ? SymbolInfoDouble(_Symbol, SYMBOL_ASK) : SymbolInfoDouble(_Symbol, SYMBOL_BID);
   
   // Position sizing - improved calculation
   double balance = AccountInfoDouble(ACCOUNT_BALANCE);
   double equity = AccountInfoDouble(ACCOUNT_EQUITY);
   double riskAmount = MathMin(balance, equity) * RiskPercent / 100;

   // Calculate lot size based on risk amount and stop loss
   double tickValue = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
   double tickSize = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);
   double lotStep = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);

   double slInPoints = sl / _Point;
   double lotSize = riskAmount / (slInPoints * tickValue);

   // Normalize to lot step
   lotSize = NormalizeDouble(MathFloor(lotSize / lotStep) * lotStep, 2);
   
   // Check minimum and maximum lot size
   double minLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
   double maxLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
   
   if (lotSize < minLot) lotSize = minLot;
   if (lotSize > maxLot) lotSize = maxLot;
   
   // Calculate SL and TP levels
   double stopLoss = (type == ORDER_TYPE_BUY) ? entry - sl : entry + sl;
   double takeProfit = (type == ORDER_TYPE_BUY) ? entry + tp : entry - tp;
   
   // Execute trade
   MqlTradeRequest request = {0};
   MqlTradeResult result = {0};
   
   request.action = TRADE_ACTION_DEAL;
   request.symbol = _Symbol;
   request.volume = lotSize;
   request.type = type;
   request.price = entry;
   request.sl = stopLoss;
   request.tp = takeProfit;
   request.deviation = 10;
   request.magic = MagicNumber;
   request.comment = TradeComment;
   request.type_filling = ORDER_FILLING_FOK;
   
   if(!OrderSend(request, result))
   {
      Print("OrderSend failed with error: ", GetLastError());
      return;
   }
   
   if (result.retcode == TRADE_RETCODE_DONE)
   {
      lastTradeTime = TimeCurrent();
      tradesToday++;
      Print("Trade executed successfully. Order: ", result.order, " Deal: ", result.deal);
   }
   else
   {
      Print("Trade failed with retcode: ", result.retcode);
   }
}

//+------------------------------------------------------------------+
//| Check for new bar                                                |
//+------------------------------------------------------------------+
bool IsNewBar()
{
   static datetime lastBar;
   datetime currentBar = iTime(_Symbol, PERIOD_M15, 0);
   
   if (lastBar != currentBar)
   {
      lastBar = currentBar;
      return true;
   }
   return false;
}

//+------------------------------------------------------------------+
//| Check if there are open positions                                |
//+------------------------------------------------------------------+
bool HasOpenPositions()
{
   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(PositionGetSymbol(i) == _Symbol && PositionGetInteger(POSITION_MAGIC) == MagicNumber)
         return true;
   }
   return false;
}

//+------------------------------------------------------------------+
//| Get current spread in points                                     |
//+------------------------------------------------------------------+
double GetSpread()
{
   double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
   double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);
   return (ask - bid) / _Point;
}

//+------------------------------------------------------------------+
//| Check if trading is allowed                                      |
//+------------------------------------------------------------------+
bool IsTradingAllowed()
{
   if(!TerminalInfoInteger(TERMINAL_TRADE_ALLOWED))
   {
      Print("Trading is not allowed in the terminal");
      return false;
   }
   
   if(!MQLInfoInteger(MQL_TRADE_ALLOWED))
   {
      Print("Trading is not allowed for this EA");
      return false;
   }
   
   if(!AccountInfoInteger(ACCOUNT_TRADE_EXPERT))
   {
      Print("Automated trading is not allowed for this account");
      return false;
   }
   
   return true;
}

//+------------------------------------------------------------------+
//| Manage existing positions (trailing stop, break-even)           |
//+------------------------------------------------------------------+
void ManagePositions()
{
   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(PositionGetSymbol(i) == _Symbol && PositionGetInteger(POSITION_MAGIC) == MagicNumber)
      {
         ulong ticket = PositionGetInteger(POSITION_TICKET);
         double openPrice = PositionGetDouble(POSITION_PRICE_OPEN);
         double currentSL = PositionGetDouble(POSITION_SL);
         ENUM_POSITION_TYPE posType = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);

         double currentPrice = (posType == POSITION_TYPE_BUY) ?
                              SymbolInfoDouble(_Symbol, SYMBOL_BID) :
                              SymbolInfoDouble(_Symbol, SYMBOL_ASK);

         double profit = (posType == POSITION_TYPE_BUY) ?
                        (currentPrice - openPrice) :
                        (openPrice - currentPrice);

         double profitPips = profit / _Point;

         // Break-even logic
         if(UseBreakEven && profitPips >= BreakEvenPips)
         {
            double newSL = openPrice;
            if((posType == POSITION_TYPE_BUY && currentSL < newSL) ||
               (posType == POSITION_TYPE_SELL && currentSL > newSL))
            {
               ModifyPosition(ticket, newSL, PositionGetDouble(POSITION_TP));
            }
         }

         // Trailing stop logic
         if(UseTrailingStop && profitPips >= TrailingStart)
         {
            double newSL;
            if(posType == POSITION_TYPE_BUY)
            {
               newSL = currentPrice - (TrailingStep * _Point);
               if(newSL > currentSL)
                  ModifyPosition(ticket, newSL, PositionGetDouble(POSITION_TP));
            }
            else
            {
               newSL = currentPrice + (TrailingStep * _Point);
               if(newSL < currentSL)
                  ModifyPosition(ticket, newSL, PositionGetDouble(POSITION_TP));
            }
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Modify position stop loss and take profit                        |
//+------------------------------------------------------------------+
bool ModifyPosition(ulong ticket, double sl, double tp)
{
   MqlTradeRequest request = {0};
   MqlTradeResult result = {0};

   request.action = TRADE_ACTION_SLTP;
   request.position = ticket;
   request.sl = sl;
   request.tp = tp;

   if(!OrderSend(request, result))
   {
      Print("Failed to modify position: ", GetLastError());
      return false;
   }

   if(result.retcode == TRADE_RETCODE_DONE)
   {
      Print("Position modified successfully. Ticket: ", ticket);
      return true;
   }
   else
   {
      Print("Position modification failed with retcode: ", result.retcode);
      return false;
   }
}
