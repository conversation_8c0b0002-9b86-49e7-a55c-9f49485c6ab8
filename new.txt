### MVP Forex EA Strategy for MT5 (Volatility Breakout with Trend Filter)

Here's a simplified yet robust EA strategy inspired by profitable principles. This MVP combines volatility-based entries with trend confirmation and strict risk management.

```mq5
//+------------------------------------------------------------------+
//|                                     VolatilityBreakoutTrend.mq5  |
//|                        Copyright 2024, MetaQuotes Software Corp. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024"
#property version   "1.00"
#property strict

// Input parameters
input double   RiskPercent      = 1.0;     // Risk per trade (%)
input int      ATR_Period       = 14;      // ATR Period
input double   ATR_Multiplier   = 1.5;     // ATR Multiplier for SL
input double   RR_Ratio         = 2.0;     // Risk:Reward Ratio
input int      EMA_Fast         = 50;      // Fast EMA
input int      EMA_Slow         = 200;     // Slow EMA
input int      Min_ATR_Pips     = 15;      // Min Volatility (pips)
input int      Max_Trades_Day   = 3;       // Max Trades/Day

// Global variables
datetime lastTradeTime;
int tradesToday;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   lastTradeTime = 0;
   tradesToday = 0;
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
   // Reset daily trade counter
   if (TimeDay(TimeCurrent()) != TimeDay(lastTradeTime))
      tradesToday = 0;

   // Check trading conditions
   if (tradesToday < Max_Trades_Day && IsNewBar())
   {
      double atr = iATR(_Symbol, PERIOD_D1, ATR_Period, 0);
      double minVolatility = Min_ATR_Pips * Point();
      
      if (atr > minVolatility)
      {
         // Trend filter
         double fastEMA = iEMA(_Symbol, PERIOD_H4, EMA_Fast, 0, PRICE_CLOSE);
         double slowEMA = iEMA(_Symbol, PERIOD_H4, EMA_Slow, 0, PRICE_CLOSE);
         
         // Entry logic
         if (fastEMA > slowEMA && IsBullishBreak()) 
            PlaceTrade(ORDER_TYPE_BUY, atr);
         else if (fastEMA < slowEMA && IsBearishBreak())
            PlaceTrade(ORDER_TYPE_SELL, atr);
      }
   }
}

//+------------------------------------------------------------------+
//| Check for bullish breakout condition                             |
//+------------------------------------------------------------------+
bool IsBullishBreak()
{
   double prevHigh = iHigh(_Symbol, PERIOD_D1, 1);
   return (Ask > prevHigh);
}

//+------------------------------------------------------------------+
//| Check for bearish breakout condition                             |
//+------------------------------------------------------------------+
bool IsBearishBreak()
{
   double prevLow = iLow(_Symbol, PERIOD_D1, 1);
   return (Bid < prevLow);
}

//+------------------------------------------------------------------+
//| Execute trade with risk management                               |
//+------------------------------------------------------------------+
void PlaceTrade(ENUM_ORDER_TYPE type, double atr)
{
   double sl = atr * ATR_Multiplier;
   double tp = sl * RR_Ratio;
   double entry = (type == ORDER_TYPE_BUY) ? Ask : Bid;
   
   // Position sizing
   double riskAmount = AccountInfoDouble(ACCOUNT_BALANCE) * RiskPercent / 100;
   double lotSize = NormalizeDouble(riskAmount / (sl / Point()), 2);
   
   if (lotSize < 0.01) lotSize = 0.01;
   
   // Execute trade
   MqlTradeRequest request = {0};
   MqlTradeResult result = {0};
   
   request.action = TRADE_ACTION_DEAL;
   request.symbol = _Symbol;
   request.volume = lotSize;
   request.type = type;
   request.price = entry;
   request.sl = (type == ORDER_TYPE_BUY) ? entry - sl : entry + sl;
   request.tp = (type == ORDER_TYPE_BUY) ? entry + tp : entry - tp;
   request.deviation = 10;
   request.type_filling = ORDER_FILLING_FOK;
   
   OrderSend(request, result);
   
   if (result.retcode == TRADE_RETCODE_DONE)
   {
      lastTradeTime = TimeCurrent();
      tradesToday++;
   }
}

//+------------------------------------------------------------------+
//| Check for new bar                                                |
//+------------------------------------------------------------------+
bool IsNewBar()
{
   static datetime lastBar;
   datetime currentBar = iTime(_Symbol, PERIOD_M15, 0);
   
   if (lastBar != currentBar)
   {
      lastBar = currentBar;
      return true;
   }
   return false;
}
```

### Strategy Logic Explained:

1. **Trend Filter**:
   - Uses 50/200 EMA crossover (H4 timeframe)
   - Only trades in direction of trend (bullish/bearish)

2. **Entry Signal**:
   - Volatility breakout of previous day's high/low
   - Requires minimum volatility (15 pips ATR filter)

3. **Risk Management**:
   - Fixed 1% risk per trade (adjustable)
   - ATR-based stop loss (1.5x daily ATR)
   - 1:2 Risk-Reward ratio
   - Max 3 trades/day to prevent overtrading

4. **Position Sizing**:
   - Dynamic lot calculation based on account balance
   - Minimum 0.01 lot protection

### Key Features:

- **Volatility Adaptation**: Uses ATR to adjust to changing market conditions
- **Trend-Following**: Avoids counter-trend trades
- **Breakout Logic**: Captures momentum after consolidation
- **Robust Risk Controls**: Multiple protective layers
- **Anti-Martingale**: Fixed percentage risk model

### Optimization Tips:

1. Test different volatility thresholds (Min_ATR_Pips)
2. Experiment with EMA combinations (e.g. 20/100, 30/150)
3. Adjust RR ratio between 1.5-3.0
4. Test session-specific entries (e.g. London/New York overlap)

### Next Steps:

1. **Backtest**: 
```mql5
Strategy Tester Settings:
- Currency Pairs: EURUSD, GBPUSD, XAUUSD
- Timeframe: M15 + H4 (multi-timeframe)
- Test Period: 2015-2025
- Spread: Variable
```

2. **Forward Testing**:
- Run on demo account for 2-3 months
- Monitor drawdown (<15%) and win rate (>55%)

3. **Capital Scaling Plan**:
```mermaid
graph LR
A[Phase 1: $5k Account] -->|Target 2%/mo| B[Phase 2: Prop Challenges]
B -->|Pass 2x$50k| C[Phase 3: Withdraw $5k/mo]
C --> D[Reinvest profits]
```

Would you like me to:
1. Enhance this with additional filters (RSI divergence, volume analysis)?
2. Prepare a backtest report template?
3. Outline the prop firm challenge execution plan?