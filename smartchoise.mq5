//+------------------------------------------------------------------+
//|                                           SmartChoise_Clone.mq5 |
//|                                    Neural Network Gold Trading  |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Your Name"
#property link      "https://www.mql5.com"
#property version   "1.00"

#include <Trade\Trade.mqh>

//--- Input Parameters
input group "=== Risk Management ==="
enum ENUM_RISK_LEVEL {
   RISK_LOW = 0,       // Low Risk
   RISK_MEDIUM = 1,    // Medium Risk  
   RISK_HIGH = 2,      // High Risk
   RISK_EXTREME = 3    // Extreme Risk
};

enum ENUM_TRADING_STYLE {
   STYLE_CONSERVATIVE = 0,  // Conservative (Fewer trades)
   STYLE_AGGRESSIVE = 1     // Aggressive (More trades)
};

input ENUM_RISK_LEVEL RiskLevel = RISK_MEDIUM;                    // Risk Level
input ENUM_TRADING_STYLE TradingStyle = STYLE_CONSERVATIVE;       // Trading Style
input double HardStopPercent = 20.0;                             // Hard Stop % (Daily Drawdown Limit)
input bool UseRecoverySystem = true;                             // Enable Recovery System
input bool UseFixedLotSize = false;                              // Use Fixed Lot Size
input double FixedLotSize = 0.01;                                // Fixed Lot Size (if enabled)

input group "=== Neural Network Settings ==="
input bool EnableNeuralNetwork = true;                           // Enable Neural Network Strategy
input double NN_ConfidenceThreshold = 0.55;                      // NN Confidence Threshold (0.5-0.9)
input int NN_LookbackPeriods = 100;                              // NN Analysis Lookback Periods

input group "=== Trading Strategies ==="
input bool EnableSupportResistance = true;                       // Enable Support/Resistance Strategy
input bool EnableCandlestickPatterns = true;                     // Enable Candlestick Pattern Strategy
input bool EnableTrendFollowing = true;                          // Enable Trend Following Strategy
input bool EnableMomentumStrategy = true;                        // Enable Momentum Strategy
input bool EnableBreakoutStrategy = true;                        // Enable Breakout Strategy

input group "=== Trade Management ==="
input int MaxSpread = 30;                                        // Max Spread (points)
input bool UseVirtualTrailingStop = true;                        // Use Virtual Trailing Stop
input double TrailingStopDistance = 20.0;                        // Trailing Stop Distance (points)
input int MaxConcurrentTrades = 3;                               // Maximum Concurrent Trades

input group "=== News Filter ==="
input bool EnableNewsFilter = false;                             // Enable News Filter
input int NewsBufferMinutes = 30;                                // News Buffer (minutes)

input group "=== Advanced Settings ==="
input int MagicNumber = 123456;                                  // Magic Number
input string TradeComment = "SmartChoise_Clone";                 // Trade Comment

//--- Global Variables
CTrade trade;
double accountBalance = 0;
double dailyStartBalance = 0;
datetime lastResetTime = 0;
int totalTrades = 0;
double totalProfit = 0;

//--- Neural Network Variables
double neuralInputs[];
double neuralWeights[];
double neuralBiases[];
bool isNeuralNetworkTrained = false;
int neuralInputSize = 20;
int neuralHiddenSize = 10;

//--- Price action variables
double currentHigh, currentLow, currentOpen, currentClose;
double previousHigh, previousLow, previousOpen, previousClose;

//--- Indicator handles
int handle_RSI, handle_Stochastic, handle_EMA20, handle_EMA50, handle_ATR, handle_MACD;
int handle_BB, handle_ADX;

//--- Trading variables
datetime lastTradeTime = 0;
int consecutiveLosses = 0;
double lastSignalPrice = 0;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit() {
    Print("SmartChoise Clone EA - Initializing...");
    
    // Initialize trade object
    trade.SetExpertMagicNumber(MagicNumber);
    trade.SetDeviationInPoints(10);
    trade.SetTypeFilling(ORDER_FILLING_IOC);
    
    // Initialize account variables
    accountBalance = AccountInfoDouble(ACCOUNT_BALANCE);
    dailyStartBalance = accountBalance;
    lastResetTime = TimeCurrent();
    
    // Initialize indicators
    InitializeIndicators();

    // Initialize neural network
    if (EnableNeuralNetwork) {
        InitializeNeuralNetwork();
    }
    
    // Validate symbol
    if (Symbol() != "XAUUSD") {
        Alert("Warning: This EA is optimized for XAUUSD (Gold). Current symbol: " + Symbol());
    }
    
    // Validate timeframe
    if (Period() != PERIOD_M1) {
        Alert("Warning: This EA is optimized for M1 timeframe. Current timeframe: " + EnumToString(Period()));
    }
    
    Print("SmartChoise Clone EA - Initialization completed successfully");
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason) {
    // Release indicator handles
    if (handle_RSI != INVALID_HANDLE) IndicatorRelease(handle_RSI);
    if (handle_Stochastic != INVALID_HANDLE) IndicatorRelease(handle_Stochastic);
    if (handle_EMA20 != INVALID_HANDLE) IndicatorRelease(handle_EMA20);
    if (handle_EMA50 != INVALID_HANDLE) IndicatorRelease(handle_EMA50);
    if (handle_ATR != INVALID_HANDLE) IndicatorRelease(handle_ATR);
    if (handle_MACD != INVALID_HANDLE) IndicatorRelease(handle_MACD);
    if (handle_BB != INVALID_HANDLE) IndicatorRelease(handle_BB);
    if (handle_ADX != INVALID_HANDLE) IndicatorRelease(handle_ADX);

    Print("SmartChoise Enhanced EA - Deinitialization. Reason: ", reason);
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick() {
    static datetime lastDebugTime = 0;
    static int tickCount = 0;
    tickCount++;

    // Update current market data
    UpdateMarketData();

    // Check daily reset
    CheckDailyReset();

    // Check hard stop
    if (CheckHardStop()) {
        Print("Hard stop triggered - Trading halted for today");
        return;
    }

    // Check spread
    if (!CheckSpread()) {
        if (TimeCurrent() - lastDebugTime > 300) { // Debug every 5 minutes
            Print("Spread too high: ", SymbolInfoInteger(Symbol(), SYMBOL_SPREAD), " > ", MaxSpread);
            lastDebugTime = TimeCurrent();
        }
        return;
    }

    // Check news filter
    if (EnableNewsFilter && !CheckNewsFilter()) {
        return;
    }

    // Update neural network inputs
    if (EnableNeuralNetwork) {
        UpdateNeuralInputs();
    }

    // Generate trading signals
    int signal = GenerateTradingSignals();

    // Debug information every 5 minutes
    if (TimeCurrent() - lastDebugTime > 300) {
        Print("EA Status - Ticks: ", tickCount, " | Open Positions: ", CountOpenPositions(),
              " | Last Signal: ", signal, " | Balance: ", AccountInfoDouble(ACCOUNT_BALANCE));
        lastDebugTime = TimeCurrent();
        tickCount = 0;
    }

    // Execute trades based on signals
    if (signal != 0) {
        ExecuteTrade(signal);
    }

    // Manage existing positions
    ManagePositions();
}

//+------------------------------------------------------------------+
//| Update market data                                               |
//+------------------------------------------------------------------+
void UpdateMarketData() {
    // Current candle
    currentHigh = iHigh(Symbol(), PERIOD_M1, 0);
    currentLow = iLow(Symbol(), PERIOD_M1, 0);
    currentOpen = iOpen(Symbol(), PERIOD_M1, 0);
    currentClose = iClose(Symbol(), PERIOD_M1, 0);
    
    // Previous candle
    previousHigh = iHigh(Symbol(), PERIOD_M1, 1);
    previousLow = iLow(Symbol(), PERIOD_M1, 1);
    previousOpen = iOpen(Symbol(), PERIOD_M1, 1);
    previousClose = iClose(Symbol(), PERIOD_M1, 1);
}

//+------------------------------------------------------------------+
//| Initialize Indicators                                            |
//+------------------------------------------------------------------+
void InitializeIndicators() {
    handle_RSI = iRSI(Symbol(), PERIOD_M1, 14, PRICE_CLOSE);
    handle_Stochastic = iStochastic(Symbol(), PERIOD_M1, 5, 3, 3, MODE_SMA, STO_LOWHIGH);
    handle_EMA20 = iMA(Symbol(), PERIOD_M1, 20, 0, MODE_EMA, PRICE_CLOSE);
    handle_EMA50 = iMA(Symbol(), PERIOD_M1, 50, 0, MODE_EMA, PRICE_CLOSE);
    handle_ATR = iATR(Symbol(), PERIOD_M1, 14);
    handle_MACD = iMACD(Symbol(), PERIOD_M1, 12, 26, 9, PRICE_CLOSE);
    handle_BB = iBands(Symbol(), PERIOD_M1, 20, 0, 2.0, PRICE_CLOSE);
    handle_ADX = iADX(Symbol(), PERIOD_M1, 14);

    // Check if all handles are valid
    if (handle_RSI == INVALID_HANDLE || handle_Stochastic == INVALID_HANDLE ||
        handle_EMA20 == INVALID_HANDLE || handle_EMA50 == INVALID_HANDLE ||
        handle_ATR == INVALID_HANDLE || handle_MACD == INVALID_HANDLE ||
        handle_BB == INVALID_HANDLE || handle_ADX == INVALID_HANDLE) {
        Print("Error: Failed to create indicator handles");
    } else {
        Print("All indicator handles created successfully");
    }
}

//+------------------------------------------------------------------+
//| Initialize Neural Network                                        |
//+------------------------------------------------------------------+
void InitializeNeuralNetwork() {
    // Initialize neural network structure
    int inputSize = neuralInputSize;   // Number of input features
    int hiddenSize = neuralHiddenSize; // Hidden layer neurons
    int outputSize = 3;                // Buy/Sell/Hold outputs

    // Resize arrays - for 2D array simulation, we use 1D array with calculated indices
    ArrayResize(neuralInputs, inputSize);
    ArrayResize(neuralWeights, hiddenSize * inputSize); // Flattened 2D array
    ArrayResize(neuralBiases, hiddenSize);

    // Initialize with random weights (simplified)
    MathSrand((int)TimeCurrent());
    for (int i = 0; i < hiddenSize; i++) {
        neuralBiases[i] = (MathRand() / 32767.0 - 0.5) * 2.0;

        for (int j = 0; j < inputSize; j++) {
            // Use flattened array index: weights[i][j] = weights[i * inputSize + j]
            int index = i * inputSize + j;
            neuralWeights[index] = (MathRand() / 32767.0 - 0.5) * 2.0;
        }
    }

    isNeuralNetworkTrained = true;
    Print("Neural Network initialized with ", inputSize, " inputs, ", hiddenSize, " hidden neurons");
}

//+------------------------------------------------------------------+
//| Update Neural Network Inputs                                    |
//+------------------------------------------------------------------+
void UpdateNeuralInputs() {
    if (ArraySize(neuralInputs) < 20) return;
    
    // Price-based inputs (normalized)
    neuralInputs[0] = NormalizePrice(currentClose - currentOpen);  // Body size
    neuralInputs[1] = NormalizePrice(currentHigh - currentLow);    // Range
    neuralInputs[2] = NormalizePrice(currentClose - previousClose); // Price change
    
    // Technical indicators - using global handles for proper indicator access
    double rsi_buffer[];
    ArraySetAsSeries(rsi_buffer, true);
    if (CopyBuffer(handle_RSI, 0, 0, 1, rsi_buffer) > 0) {
        neuralInputs[3] = NormalizeIndicator(rsi_buffer[0], 0, 100);
    } else {
        neuralInputs[3] = 0.5; // Default neutral value
    }

    double stoch_buffer[];
    ArraySetAsSeries(stoch_buffer, true);
    if (CopyBuffer(handle_Stochastic, 0, 0, 1, stoch_buffer) > 0) {
        neuralInputs[4] = NormalizeIndicator(stoch_buffer[0], 0, 100);
    } else {
        neuralInputs[4] = 0.5; // Default neutral value
    }

    // Moving averages
    double ema20_buffer[], ema50_buffer[];
    ArraySetAsSeries(ema20_buffer, true);
    ArraySetAsSeries(ema50_buffer, true);

    double ema20 = 0, ema50 = 0;
    if (CopyBuffer(handle_EMA20, 0, 0, 1, ema20_buffer) > 0) {
        ema20 = ema20_buffer[0];
    }
    if (CopyBuffer(handle_EMA50, 0, 0, 1, ema50_buffer) > 0) {
        ema50 = ema50_buffer[0];
    }

    neuralInputs[5] = NormalizePrice(currentClose - ema20);
    neuralInputs[6] = NormalizePrice(ema20 - ema50);

    // Volatility
    double atr_buffer[];
    ArraySetAsSeries(atr_buffer, true);
    double atr = 0;
    if (CopyBuffer(handle_ATR, 0, 0, 1, atr_buffer) > 0) {
        atr = atr_buffer[0];
    }
    neuralInputs[7] = NormalizePrice(atr);

    // Volume (if available)
    long volume_buffer[];
    ArraySetAsSeries(volume_buffer, true);
    if (CopyTickVolume(Symbol(), PERIOD_M1, 0, 1, volume_buffer) > 0) {
        neuralInputs[8] = NormalizeVolume(volume_buffer[0]);
    } else {
        neuralInputs[8] = 0.5; // Default neutral value
    }

    // Time-based features
    MqlDateTime dt;
    TimeToStruct(TimeCurrent(), dt);
    neuralInputs[9] = dt.hour / 24.0;  // Hour of day
    neuralInputs[10] = dt.day_of_week / 7.0;  // Day of week

    // Pattern recognition inputs
    neuralInputs[11] = DetectDoji() ? 1.0 : 0.0;
    neuralInputs[12] = DetectHammer() ? 1.0 : 0.0;
    neuralInputs[13] = DetectEngulfing() ? 1.0 : 0.0;

    // Market structure
    neuralInputs[14] = IsUptrend() ? 1.0 : 0.0;
    neuralInputs[15] = IsDowntrend() ? 1.0 : 0.0;

    // Support/Resistance proximity
    neuralInputs[16] = GetSupportResistanceStrength();

    // Momentum indicators
    double macd_buffer[];
    ArraySetAsSeries(macd_buffer, true);
    double macd = 0;
    if (CopyBuffer(handle_MACD, 0, 0, 1, macd_buffer) > 0) {
        macd = macd_buffer[0];
    }
    neuralInputs[17] = NormalizePrice(macd);
    
    // Additional features
    neuralInputs[18] = GetMarketVolatility();
    neuralInputs[19] = GetTrendStrength();
}

//+------------------------------------------------------------------+
//| Generate Trading Signals                                         |
//+------------------------------------------------------------------+
int GenerateTradingSignals() {
    int signal = 0;
    double confidence = 0.0;
    int signalStrength = 0;

    // Prevent too frequent trading
    if (TimeCurrent() - lastTradeTime < 300) { // 5 minutes minimum between trades
        return 0;
    }

    // Multiple strategy approach with voting system
    int buyVotes = 0, sellVotes = 0;

    // 1. Neural Network Signal
    if (EnableNeuralNetwork && isNeuralNetworkTrained) {
        int nnSignal = GetNeuralNetworkSignal(confidence);
        if (confidence >= NN_ConfidenceThreshold) {
            if (nnSignal > 0) buyVotes += 2;
            else if (nnSignal < 0) sellVotes += 2;
        }
    }

    // 2. Trend Following Strategy
    if (EnableTrendFollowing) {
        int trendSignal = GetTrendFollowingSignal();
        if (trendSignal > 0) buyVotes += 1;
        else if (trendSignal < 0) sellVotes += 1;
    }

    // 3. Momentum Strategy
    if (EnableMomentumStrategy) {
        int momentumSignal = GetMomentumSignal();
        if (momentumSignal > 0) buyVotes += 1;
        else if (momentumSignal < 0) sellVotes += 1;
    }

    // 4. Breakout Strategy
    if (EnableBreakoutStrategy) {
        int breakoutSignal = GetBreakoutSignal();
        if (breakoutSignal > 0) buyVotes += 1;
        else if (breakoutSignal < 0) sellVotes += 1;
    }

    // 5. Support/Resistance Signal
    if (EnableSupportResistance) {
        int srSignal = GetSupportResistanceSignal();
        if (srSignal > 0) buyVotes += 1;
        else if (srSignal < 0) sellVotes += 1;
    }

    // 6. Candlestick Pattern Signal
    if (EnableCandlestickPatterns) {
        int candleSignal = GetCandlestickSignal();
        if (candleSignal > 0) buyVotes += 1;
        else if (candleSignal < 0) sellVotes += 1;
    }

    // Determine final signal based on votes
    int totalVotes = buyVotes + sellVotes;
    int minVotes = (TradingStyle == STYLE_AGGRESSIVE) ? 1 : 2; // Aggressive style needs fewer votes

    if (totalVotes >= minVotes) {
        if (buyVotes > sellVotes && buyVotes >= minVotes) {
            signal = 1;
            signalStrength = buyVotes;
        } else if (sellVotes > buyVotes && sellVotes >= minVotes) {
            signal = -1;
            signalStrength = sellVotes;
        }
    }

    // Fallback simple strategy if no signals generated
    if (signal == 0 && totalVotes == 0) {
        signal = GetSimpleFallbackSignal();
        if (signal != 0) {
            signalStrength = 1;
            Print("Using fallback strategy signal: ", signal);
        }
    }

    // Apply trading style filter
    if (TradingStyle == STYLE_CONSERVATIVE) {
        // More restrictive conditions for conservative style
        if (signalStrength < 3) signal = 0;
    }

    // Additional filters
    if (signal != 0) {
        // Check if price moved significantly since last signal
        double currentPrice = (signal > 0) ? SymbolInfoDouble(Symbol(), SYMBOL_ASK) : SymbolInfoDouble(Symbol(), SYMBOL_BID);
        if (MathAbs(currentPrice - lastSignalPrice) < SymbolInfoDouble(Symbol(), SYMBOL_POINT) * 50) {
            signal = 0; // Too close to last signal price
        } else {
            lastSignalPrice = currentPrice;
        }
    }

    if (signal != 0) {
        Print("Trading Signal Generated: ", signal, " | Strength: ", signalStrength, " | Buy Votes: ", buyVotes, " | Sell Votes: ", sellVotes);
    }

    return signal;
}

//+------------------------------------------------------------------+
//| Get Neural Network Signal                                        |
//+------------------------------------------------------------------+
int GetNeuralNetworkSignal(double &confidence) {
    if (!isNeuralNetworkTrained) return 0;
    
    // Simple feedforward network calculation
    double hiddenOutputs[];
    ArrayResize(hiddenOutputs, ArraySize(neuralBiases));
    
    // Calculate hidden layer using flattened array
    for (int i = 0; i < ArraySize(neuralBiases); i++) {
        hiddenOutputs[i] = neuralBiases[i];
        for (int j = 0; j < ArraySize(neuralInputs); j++) {
            // Access flattened 2D array: weights[i][j] = weights[i * inputSize + j]
            int index = i * neuralInputSize + j;
            if (index < ArraySize(neuralWeights)) {
                hiddenOutputs[i] += neuralInputs[j] * neuralWeights[index];
            }
        }
        hiddenOutputs[i] = tanh(hiddenOutputs[i]); // Activation function
    }
    
    // Simple output calculation (buy/sell probability)
    double buyProb = 0, sellProb = 0, holdProb = 0;
    
    for (int i = 0; i < ArraySize(hiddenOutputs); i++) {
        buyProb += hiddenOutputs[i] * 0.3;   // Simplified weights
        sellProb += hiddenOutputs[i] * -0.3;
        holdProb += hiddenOutputs[i] * 0.1;
    }
    
    // Normalize probabilities
    double total = MathAbs(buyProb) + MathAbs(sellProb) + MathAbs(holdProb);
    if (total > 0) {
        buyProb = MathAbs(buyProb) / total;
        sellProb = MathAbs(sellProb) / total;
        holdProb = MathAbs(holdProb) / total;
    }
    
    // Determine signal and confidence
    if (buyProb > sellProb && buyProb > holdProb) {
        confidence = buyProb;
        return 1; // Buy signal
    } else if (sellProb > buyProb && sellProb > holdProb) {
        confidence = sellProb;
        return -1; // Sell signal
    } else {
        confidence = holdProb;
        return 0; // Hold
    }
}

//+------------------------------------------------------------------+
//| Calculate Dynamic Lot Size                                       |
//+------------------------------------------------------------------+
double CalculateLotSize(int signal) {
    if (UseFixedLotSize) return FixedLotSize;
    
    double riskPercent = 0;
    
    // Risk based on risk level
    switch (RiskLevel) {
        case RISK_LOW: riskPercent = 0.5; break;
        case RISK_MEDIUM: riskPercent = 1.0; break;
        case RISK_HIGH: riskPercent = 2.0; break;
        case RISK_EXTREME: riskPercent = 4.0; break;
    }
    
    // Adjust for trading style
    if (TradingStyle == STYLE_AGGRESSIVE) {
        riskPercent *= 1.5;
    }

    // Apply recovery system - reduce risk after consecutive losses
    if (UseRecoverySystem && consecutiveLosses > 0) {
        double reductionFactor = 1.0 / (1.0 + consecutiveLosses * 0.3); // Reduce by 30% per loss
        riskPercent *= reductionFactor;
        Print("Risk reduced due to ", consecutiveLosses, " consecutive losses. New risk: ", riskPercent, "%");
    }

    // Get current balance
    double balance = AccountInfoDouble(ACCOUNT_BALANCE);
    
    // Calculate risk amount
    double riskAmount = balance * riskPercent / 100.0;
    
    // Get ATR for stop loss calculation
    double atr_buffer[];
    ArraySetAsSeries(atr_buffer, true);
    double atr = 0;
    if (CopyBuffer(handle_ATR, 0, 0, 1, atr_buffer) > 0) {
        atr = atr_buffer[0];
    }
    double stopLoss = atr * 2.0; // 2 ATR stop loss
    
    // Calculate lot size
    double tickValue = SymbolInfoDouble(Symbol(), SYMBOL_TRADE_TICK_VALUE);
    double tickSize = SymbolInfoDouble(Symbol(), SYMBOL_TRADE_TICK_SIZE);
    double lotSize = riskAmount / (stopLoss / tickSize * tickValue);
    
    // Apply min/max lot constraints
    double minLot = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MIN);
    double maxLot = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MAX);
    double lotStep = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_STEP);
    
    lotSize = MathMax(minLot, MathMin(maxLot, lotSize));
    lotSize = MathFloor(lotSize / lotStep) * lotStep;
    
    return lotSize;
}

//+------------------------------------------------------------------+
//| Execute Trade                                                    |
//+------------------------------------------------------------------+
void ExecuteTrade(int signal) {
    // Check maximum concurrent trades
    if (CountOpenPositions() >= MaxConcurrentTrades) {
        Print("Maximum concurrent trades reached: ", MaxConcurrentTrades);
        return;
    }

    double lotSize = CalculateLotSize(signal);
    double price = (signal > 0) ? SymbolInfoDouble(Symbol(), SYMBOL_ASK) : SymbolInfoDouble(Symbol(), SYMBOL_BID);

    // Calculate stop loss and take profit
    double atr_buffer[];
    ArraySetAsSeries(atr_buffer, true);
    double atr = 0;
    if (CopyBuffer(handle_ATR, 0, 0, 1, atr_buffer) > 0) {
        atr = atr_buffer[0];
    }

    // Ensure minimum ATR value
    if (atr < SymbolInfoDouble(Symbol(), SYMBOL_POINT) * 100) {
        atr = SymbolInfoDouble(Symbol(), SYMBOL_POINT) * 200; // Minimum 20 points
    }

    double stopLoss, takeProfit;
    bool tradeResult = false;

    if (signal > 0) { // Buy
        stopLoss = price - (atr * 2.0);
        takeProfit = price + (atr * 3.0); // 1.5:1 risk/reward

        // Adjust for symbol's minimum stop level
        double minStopLevel = SymbolInfoInteger(Symbol(), SYMBOL_TRADE_STOPS_LEVEL) * SymbolInfoDouble(Symbol(), SYMBOL_POINT);
        if (price - stopLoss < minStopLevel) {
            stopLoss = price - minStopLevel;
        }
        if (takeProfit - price < minStopLevel) {
            takeProfit = price + minStopLevel;
        }

        string comment = TradeComment + "_" + IntegerToString(RiskLevel) +
                        (TradingStyle == STYLE_AGGRESSIVE ? "A" : "C") + "_Multi";

        tradeResult = trade.Buy(lotSize, Symbol(), price, stopLoss, takeProfit, comment);

        if (tradeResult) {
            Print("BUY order executed: Lot=", lotSize, " Price=", price, " SL=", stopLoss, " TP=", takeProfit);
            lastTradeTime = TimeCurrent();
        } else {
            Print("BUY order failed: ", trade.ResultRetcode(), " - ", trade.ResultRetcodeDescription());
        }

    } else if (signal < 0) { // Sell
        stopLoss = price + (atr * 2.0);
        takeProfit = price - (atr * 3.0);

        // Adjust for symbol's minimum stop level
        double minStopLevel = SymbolInfoInteger(Symbol(), SYMBOL_TRADE_STOPS_LEVEL) * SymbolInfoDouble(Symbol(), SYMBOL_POINT);
        if (stopLoss - price < minStopLevel) {
            stopLoss = price + minStopLevel;
        }
        if (price - takeProfit < minStopLevel) {
            takeProfit = price - minStopLevel;
        }

        string comment = TradeComment + "_" + IntegerToString(RiskLevel) +
                        (TradingStyle == STYLE_AGGRESSIVE ? "A" : "C") + "_Multi";

        tradeResult = trade.Sell(lotSize, Symbol(), price, stopLoss, takeProfit, comment);

        if (tradeResult) {
            Print("SELL order executed: Lot=", lotSize, " Price=", price, " SL=", stopLoss, " TP=", takeProfit);
            lastTradeTime = TimeCurrent();
        } else {
            Print("SELL order failed: ", trade.ResultRetcode(), " - ", trade.ResultRetcodeDescription());
        }
    }
}

//+------------------------------------------------------------------+
//| Manage Positions                                                 |
//+------------------------------------------------------------------+
void ManagePositions() {
    for (int i = PositionsTotal() - 1; i >= 0; i--) {
        if (PositionSelectByTicket(PositionGetTicket(i))) {
            if (PositionGetString(POSITION_SYMBOL) == Symbol() && 
                PositionGetInteger(POSITION_MAGIC) == MagicNumber) {
                
                // Apply virtual trailing stop
                if (UseVirtualTrailingStop) {
                    ApplyVirtualTrailingStop();
                }
                
                // Recovery system logic
                if (UseRecoverySystem) {
                    ApplyRecoveryLogic();
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Check Hard Stop                                                  |
//+------------------------------------------------------------------+
bool CheckHardStop() {
    double currentBalance = AccountInfoDouble(ACCOUNT_BALANCE);
    double drawdown = (dailyStartBalance - currentBalance) / dailyStartBalance * 100;
    
    return (drawdown >= HardStopPercent);
}

//+------------------------------------------------------------------+
//| Check Daily Reset                                                |
//+------------------------------------------------------------------+
void CheckDailyReset() {
    MqlDateTime current, last;
    TimeToStruct(TimeCurrent(), current);
    TimeToStruct(lastResetTime, last);
    
    if (current.day != last.day) {
        dailyStartBalance = AccountInfoDouble(ACCOUNT_BALANCE);
        lastResetTime = TimeCurrent();
        Print("Daily reset - New balance: ", dailyStartBalance);
    }
}

//+------------------------------------------------------------------+
//| Helper Functions                                                 |
//+------------------------------------------------------------------+
double NormalizePrice(const double value) {
    double point = SymbolInfoDouble(Symbol(), SYMBOL_POINT);
    return value / (point * 1000); // Normalize to reasonable range
}

double NormalizeIndicator(const double value, const double min, const double max) {
    return (value - min) / (max - min);
}

double NormalizeVolume(const long volume) {
    return MathLog((double)(volume + 1)) / 10.0; // Log normalization
}

bool DetectDoji() {
    double body = MathAbs(currentClose - currentOpen);
    double range = currentHigh - currentLow;
    return (range > 0 && body / range < 0.1);
}

bool DetectHammer() {
    double body = MathAbs(currentClose - currentOpen);
    double lowerShadow = MathMin(currentOpen, currentClose) - currentLow;
    return (lowerShadow > body * 2);
}

bool DetectEngulfing() {
    double currentBody = MathAbs(currentClose - currentOpen);
    double previousBody = MathAbs(previousClose - previousOpen);
    return (currentBody > previousBody * 1.2);
}

bool DetectBullishEngulfing() {
    // Previous candle is bearish, current is bullish and engulfs previous
    return (previousClose < previousOpen && // Previous bearish
            currentClose > currentOpen &&   // Current bullish
            currentOpen < previousClose &&  // Current opens below previous close
            currentClose > previousOpen &&  // Current closes above previous open
            (currentClose - currentOpen) > (previousOpen - previousClose) * 1.1);
}

bool DetectBearishEngulfing() {
    // Previous candle is bullish, current is bearish and engulfs previous
    return (previousClose > previousOpen && // Previous bullish
            currentClose < currentOpen &&   // Current bearish
            currentOpen > previousClose &&  // Current opens above previous close
            currentClose < previousOpen &&  // Current closes below previous open
            (currentOpen - currentClose) > (previousClose - previousOpen) * 1.1);
}

bool DetectShootingStar() {
    double body = MathAbs(currentClose - currentOpen);
    double upperShadow = currentHigh - MathMax(currentOpen, currentClose);
    double lowerShadow = MathMin(currentOpen, currentClose) - currentLow;
    return (upperShadow > body * 2 && lowerShadow < body * 0.5);
}

bool DetectMorningStar() {
    // Simplified morning star: bearish, small body, bullish
    double highs[], lows[], opens[], closes[];
    ArraySetAsSeries(highs, true);
    ArraySetAsSeries(lows, true);
    ArraySetAsSeries(opens, true);
    ArraySetAsSeries(closes, true);

    if (CopyHigh(Symbol(), PERIOD_M1, 0, 3, highs) < 3 ||
        CopyLow(Symbol(), PERIOD_M1, 0, 3, lows) < 3 ||
        CopyOpen(Symbol(), PERIOD_M1, 0, 3, opens) < 3 ||
        CopyClose(Symbol(), PERIOD_M1, 0, 3, closes) < 3) {
        return false;
    }

    // Three candles: [2] bearish, [1] small, [0] bullish
    bool firstBearish = closes[2] < opens[2];
    bool middleSmall = MathAbs(closes[1] - opens[1]) < MathAbs(closes[2] - opens[2]) * 0.3;
    bool lastBullish = closes[0] > opens[0] && closes[0] > (closes[2] + opens[2]) / 2;

    return (firstBearish && middleSmall && lastBullish);
}

bool DetectEveningStar() {
    // Simplified evening star: bullish, small body, bearish
    double highs[], lows[], opens[], closes[];
    ArraySetAsSeries(highs, true);
    ArraySetAsSeries(lows, true);
    ArraySetAsSeries(opens, true);
    ArraySetAsSeries(closes, true);

    if (CopyHigh(Symbol(), PERIOD_M1, 0, 3, highs) < 3 ||
        CopyLow(Symbol(), PERIOD_M1, 0, 3, lows) < 3 ||
        CopyOpen(Symbol(), PERIOD_M1, 0, 3, opens) < 3 ||
        CopyClose(Symbol(), PERIOD_M1, 0, 3, closes) < 3) {
        return false;
    }

    // Three candles: [2] bullish, [1] small, [0] bearish
    bool firstBullish = closes[2] > opens[2];
    bool middleSmall = MathAbs(closes[1] - opens[1]) < MathAbs(closes[2] - opens[2]) * 0.3;
    bool lastBearish = closes[0] < opens[0] && closes[0] < (closes[2] + opens[2]) / 2;

    return (firstBullish && middleSmall && lastBearish);
}

bool IsNearSupport() {
    double lows[];
    ArraySetAsSeries(lows, true);
    if (CopyLow(Symbol(), PERIOD_M1, 1, 20, lows) < 20) return false;

    double support = lows[ArrayMinimum(lows, 0, 20)];
    double atr_buffer[];
    ArraySetAsSeries(atr_buffer, true);
    if (CopyBuffer(handle_ATR, 0, 0, 1, atr_buffer) < 1) return false;

    return (currentLow <= support + atr_buffer[0] * 0.5);
}

bool IsNearResistance() {
    double highs[];
    ArraySetAsSeries(highs, true);
    if (CopyHigh(Symbol(), PERIOD_M1, 1, 20, highs) < 20) return false;

    double resistance = highs[ArrayMaximum(highs, 0, 20)];
    double atr_buffer[];
    ArraySetAsSeries(atr_buffer, true);
    if (CopyBuffer(handle_ATR, 0, 0, 1, atr_buffer) < 1) return false;

    return (currentHigh >= resistance - atr_buffer[0] * 0.5);
}

//+------------------------------------------------------------------+
//| Simple Fallback Strategy                                         |
//+------------------------------------------------------------------+
int GetSimpleFallbackSignal() {
    // Simple EMA crossover strategy as fallback
    double ema20_buffer[], ema50_buffer[];
    ArraySetAsSeries(ema20_buffer, true);
    ArraySetAsSeries(ema50_buffer, true);

    if (CopyBuffer(handle_EMA20, 0, 0, 3, ema20_buffer) < 3 ||
        CopyBuffer(handle_EMA50, 0, 0, 3, ema50_buffer) < 3) {
        return 0;
    }

    // Check for EMA crossover
    bool bullishCross = (ema20_buffer[0] > ema50_buffer[0] && ema20_buffer[1] <= ema50_buffer[1]);
    bool bearishCross = (ema20_buffer[0] < ema50_buffer[0] && ema20_buffer[1] >= ema50_buffer[1]);

    // Additional confirmation: price should be moving in the same direction
    if (bullishCross && currentClose > currentOpen && currentClose > ema20_buffer[0]) {
        return 1;
    }
    if (bearishCross && currentClose < currentOpen && currentClose < ema20_buffer[0]) {
        return -1;
    }

    return 0;
}

bool IsUptrend() {
    double ema20_buffer[], ema50_buffer[];
    ArraySetAsSeries(ema20_buffer, true);
    ArraySetAsSeries(ema50_buffer, true);

    double ema20 = 0, ema50 = 0;
    if (CopyBuffer(handle_EMA20, 0, 0, 1, ema20_buffer) > 0) {
        ema20 = ema20_buffer[0];
    }
    if (CopyBuffer(handle_EMA50, 0, 0, 1, ema50_buffer) > 0) {
        ema50 = ema50_buffer[0];
    }

    return (ema20 > ema50 && currentClose > ema20);
}

bool IsDowntrend() {
    double ema20_buffer[], ema50_buffer[];
    ArraySetAsSeries(ema20_buffer, true);
    ArraySetAsSeries(ema50_buffer, true);

    double ema20 = 0, ema50 = 0;
    if (CopyBuffer(handle_EMA20, 0, 0, 1, ema20_buffer) > 0) {
        ema20 = ema20_buffer[0];
    }
    if (CopyBuffer(handle_EMA50, 0, 0, 1, ema50_buffer) > 0) {
        ema50 = ema50_buffer[0];
    }

    return (ema20 < ema50 && currentClose < ema20);
}

double GetSupportResistanceStrength() {
    // Simplified S/R calculation
    return 0.5; // Placeholder
}

double GetMarketVolatility() {
    double atr_buffer[], avgATR_buffer[];
    ArraySetAsSeries(atr_buffer, true);
    ArraySetAsSeries(avgATR_buffer, true);

    int avgATR_handle = iATR(Symbol(), PERIOD_M1, 50);

    double atr = 0, avgATR = 0;
    if (CopyBuffer(handle_ATR, 0, 0, 1, atr_buffer) > 0) {
        atr = atr_buffer[0];
    }
    if (CopyBuffer(avgATR_handle, 0, 0, 1, avgATR_buffer) > 0) {
        avgATR = avgATR_buffer[0];
    }

    return (avgATR > 0) ? atr / avgATR : 1.0;
}

double GetTrendStrength() {
    double ema20_buffer[], ema50_buffer[];
    ArraySetAsSeries(ema20_buffer, true);
    ArraySetAsSeries(ema50_buffer, true);

    double ema20 = 0, ema50 = 0;
    if (CopyBuffer(handle_EMA20, 0, 0, 1, ema20_buffer) > 0) {
        ema20 = ema20_buffer[0];
    }
    if (CopyBuffer(handle_EMA50, 0, 0, 1, ema50_buffer) > 0) {
        ema50 = ema50_buffer[0];
    }

    return (ema50 > 0) ? MathAbs(ema20 - ema50) / ema50 : 0.0;
}

//+------------------------------------------------------------------+
//| Trend Following Strategy                                         |
//+------------------------------------------------------------------+
int GetTrendFollowingSignal() {
    double ema20_buffer[], ema50_buffer[];
    ArraySetAsSeries(ema20_buffer, true);
    ArraySetAsSeries(ema50_buffer, true);

    if (CopyBuffer(handle_EMA20, 0, 0, 3, ema20_buffer) < 3 ||
        CopyBuffer(handle_EMA50, 0, 0, 3, ema50_buffer) < 3) {
        return 0;
    }

    // Strong trend conditions
    bool strongUptrend = (ema20_buffer[0] > ema50_buffer[0] &&
                         ema20_buffer[1] > ema50_buffer[1] &&
                         ema20_buffer[0] > ema20_buffer[1] &&
                         currentClose > ema20_buffer[0]);

    bool strongDowntrend = (ema20_buffer[0] < ema50_buffer[0] &&
                           ema20_buffer[1] < ema50_buffer[1] &&
                           ema20_buffer[0] < ema20_buffer[1] &&
                           currentClose < ema20_buffer[0]);

    if (strongUptrend) return 1;
    if (strongDowntrend) return -1;
    return 0;
}

//+------------------------------------------------------------------+
//| Momentum Strategy                                                |
//+------------------------------------------------------------------+
int GetMomentumSignal() {
    double rsi_buffer[], macd_buffer[], macd_signal_buffer[];
    ArraySetAsSeries(rsi_buffer, true);
    ArraySetAsSeries(macd_buffer, true);
    ArraySetAsSeries(macd_signal_buffer, true);

    if (CopyBuffer(handle_RSI, 0, 0, 2, rsi_buffer) < 2 ||
        CopyBuffer(handle_MACD, 0, 0, 2, macd_buffer) < 2 ||
        CopyBuffer(handle_MACD, 1, 0, 2, macd_signal_buffer) < 2) {
        return 0;
    }

    // RSI momentum
    bool rsi_bullish = (rsi_buffer[0] > 50 && rsi_buffer[0] > rsi_buffer[1] && rsi_buffer[0] < 80);
    bool rsi_bearish = (rsi_buffer[0] < 50 && rsi_buffer[0] < rsi_buffer[1] && rsi_buffer[0] > 20);

    // MACD momentum
    bool macd_bullish = (macd_buffer[0] > macd_signal_buffer[0] && macd_buffer[1] <= macd_signal_buffer[1]);
    bool macd_bearish = (macd_buffer[0] < macd_signal_buffer[0] && macd_buffer[1] >= macd_signal_buffer[1]);

    if (rsi_bullish && macd_bullish) return 1;
    if (rsi_bearish && macd_bearish) return -1;
    return 0;
}

//+------------------------------------------------------------------+
//| Breakout Strategy                                                |
//+------------------------------------------------------------------+
int GetBreakoutSignal() {
    double bb_upper[], bb_lower[], bb_middle[];
    double atr_buffer[];
    ArraySetAsSeries(bb_upper, true);
    ArraySetAsSeries(bb_lower, true);
    ArraySetAsSeries(bb_middle, true);
    ArraySetAsSeries(atr_buffer, true);

    if (CopyBuffer(handle_BB, 1, 0, 2, bb_upper) < 2 ||
        CopyBuffer(handle_BB, 2, 0, 2, bb_lower) < 2 ||
        CopyBuffer(handle_BB, 0, 0, 2, bb_middle) < 2 ||
        CopyBuffer(handle_ATR, 0, 0, 1, atr_buffer) < 1) {
        return 0;
    }

    double atr = atr_buffer[0];
    double volatility_threshold = atr * 1.5;

    // Breakout above upper band with volume
    if (currentClose > bb_upper[0] && previousClose <= bb_upper[1] &&
        (currentHigh - currentLow) > volatility_threshold) {
        return 1;
    }

    // Breakout below lower band with volume
    if (currentClose < bb_lower[0] && previousClose >= bb_lower[1] &&
        (currentHigh - currentLow) > volatility_threshold) {
        return -1;
    }

    return 0;
}

//+------------------------------------------------------------------+
//| Support/Resistance Strategy                                      |
//+------------------------------------------------------------------+
int GetSupportResistanceSignal() {
    // Simple S/R based on recent highs and lows
    double highs[], lows[];
    ArraySetAsSeries(highs, true);
    ArraySetAsSeries(lows, true);

    if (CopyHigh(Symbol(), PERIOD_M1, 1, 20, highs) < 20 ||
        CopyLow(Symbol(), PERIOD_M1, 1, 20, lows) < 20) {
        return 0;
    }

    // Find recent resistance and support levels
    double resistance = highs[ArrayMaximum(highs, 0, 20)];
    double support = lows[ArrayMinimum(lows, 0, 20)];

    double atr_buffer[];
    ArraySetAsSeries(atr_buffer, true);
    if (CopyBuffer(handle_ATR, 0, 0, 1, atr_buffer) < 1) return 0;

    double atr = atr_buffer[0];
    double threshold = atr * 0.5;

    // Bounce from support
    if (currentLow <= support + threshold && currentClose > support + threshold &&
        previousClose <= support + threshold) {
        return 1;
    }

    // Rejection from resistance
    if (currentHigh >= resistance - threshold && currentClose < resistance - threshold &&
        previousClose >= resistance - threshold) {
        return -1;
    }

    return 0;
}

//+------------------------------------------------------------------+
//| Enhanced Candlestick Pattern Strategy                           |
//+------------------------------------------------------------------+
int GetCandlestickSignal() {
    int signal = 0;

    // Bullish patterns
    if (DetectHammer() && IsNearSupport()) signal += 1;
    if (DetectBullishEngulfing()) signal += 1;
    if (DetectMorningStar()) signal += 1;

    // Bearish patterns
    if (DetectShootingStar() && IsNearResistance()) signal -= 1;
    if (DetectBearishEngulfing()) signal -= 1;
    if (DetectEveningStar()) signal -= 1;

    return signal;
}

bool CheckSpread() {
    double spread = SymbolInfoInteger(Symbol(), SYMBOL_SPREAD);
    return (spread <= MaxSpread);
}

bool CheckNewsFilter() {
    // Placeholder for news filter logic
    return true;
}

int CountOpenPositions() {
    int count = 0;
    for (int i = 0; i < PositionsTotal(); i++) {
        if (PositionSelectByTicket(PositionGetTicket(i))) {
            if (PositionGetString(POSITION_SYMBOL) == Symbol() && 
                PositionGetInteger(POSITION_MAGIC) == MagicNumber) {
                count++;
            }
        }
    }
    return count;
}

//+------------------------------------------------------------------+
//| Apply Virtual Trailing Stop                                     |
//+------------------------------------------------------------------+
void ApplyVirtualTrailingStop() {
    if (!UseVirtualTrailingStop) return;

    for (int i = 0; i < PositionsTotal(); i++) {
        if (PositionSelectByTicket(PositionGetTicket(i))) {
            if (PositionGetString(POSITION_SYMBOL) == Symbol() &&
                PositionGetInteger(POSITION_MAGIC) == MagicNumber) {

                double currentPrice = PositionGetDouble(POSITION_PRICE_CURRENT);
                double openPrice = PositionGetDouble(POSITION_PRICE_OPEN);
                double currentSL = PositionGetDouble(POSITION_SL);
                ENUM_POSITION_TYPE posType = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);

                double atr_buffer[];
                ArraySetAsSeries(atr_buffer, true);
                if (CopyBuffer(handle_ATR, 0, 0, 1, atr_buffer) < 1) continue;

                double atr = atr_buffer[0];
                double trailDistance = atr * 1.5; // 1.5 ATR trailing distance

                if (posType == POSITION_TYPE_BUY) {
                    double newSL = currentPrice - trailDistance;
                    if (newSL > currentSL && newSL > openPrice) {
                        trade.PositionModify(PositionGetTicket(i), newSL, PositionGetDouble(POSITION_TP));
                        Print("Trailing stop updated for BUY position: ", PositionGetTicket(i), " New SL: ", newSL);
                    }
                } else if (posType == POSITION_TYPE_SELL) {
                    double newSL = currentPrice + trailDistance;
                    if (newSL < currentSL && newSL < openPrice) {
                        trade.PositionModify(PositionGetTicket(i), newSL, PositionGetDouble(POSITION_TP));
                        Print("Trailing stop updated for SELL position: ", PositionGetTicket(i), " New SL: ", newSL);
                    }
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Apply Recovery Logic                                             |
//+------------------------------------------------------------------+
void ApplyRecoveryLogic() {
    if (!UseRecoverySystem) return;

    // Simple recovery: reduce lot size after consecutive losses
    static int lastTradeResult = 0; // 1 = profit, -1 = loss, 0 = unknown

    // Check if any position was just closed
    static int lastPositionCount = 0;
    int currentPositionCount = CountOpenPositions();

    if (currentPositionCount < lastPositionCount) {
        // A position was closed, check if it was profitable
        // This is a simplified approach - in practice, you'd track individual trades
        double currentBalance = AccountInfoDouble(ACCOUNT_BALANCE);
        static double lastBalance = currentBalance;

        if (currentBalance > lastBalance) {
            consecutiveLosses = 0; // Reset on profit
            Print("Trade closed with profit. Consecutive losses reset.");
        } else {
            consecutiveLosses++;
            Print("Trade closed with loss. Consecutive losses: ", consecutiveLosses);
        }

        lastBalance = currentBalance;
    }

    lastPositionCount = currentPositionCount;
}